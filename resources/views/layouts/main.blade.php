<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

	<meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Nautilus - {{$title}}</title>

    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    {{-- <link rel="manifest" href="site.webmanifest"> --}}
    <link rel="icon" href="{{ asset ("assets/nautilus_logo.png")}}" type="image/gif" sizes="16x16">
	<link rel="mask-icon" href="safari-pinned-tab.svg" color="#ffcf30">
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">

    <!-- Core JS files -->
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/loaders/pace.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/core/libraries/jquery.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/core/libraries/bootstrap.min.js") }}"></script>
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/loaders/blockui.min.js") }}"></script>
	<!-- /core JS files -->

    <!-- Theme JS files -->    
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/selects/select2.min.js") }}"></script>
    <script type="text/javascript" src="{{ asset ("assets/js/core/app.js") }}"></script>
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/ui/ripple.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/notifications/pnotify.min.js") }}"></script>
	<!-- /theme JS files -->

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.12.0/css/all.css">
    <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet">
    <link href="{{ asset('assets/css/icons/fontawesome/styles.min.css') }}" rel="stylesheet" type="text/css">
    <!-- Fonts -->

    <!-- Global stylesheets -->
	<link href="{{ asset ("assets/css/icons/icomoon/styles.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/bootstrap.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/core.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/components.css") }}" rel="stylesheet" type="text/css">
    <link href="{{ asset ("assets/css/colors.css") }}" rel="stylesheet" type="text/css">
	<link href="{{ asset ("assets/css/extras/animate.min.css") }}" rel="stylesheet" type="text/css">
	<!-- /global stylesheets -->

</head>


<style>
	
</style>


<script>
    $( document ).ready(function() {
        $('.select').select2({
            minimumResultsForSearch: Infinity
        });
        $('.select-search').select2();

        function notify(title, text, icon, color) {
            new PNotify({
                title: title,
                text: text,
                icon: icon,
                addclass: color
            });
        }
    });
    
    
</script>
    

<body class="navbar-top">

    <!-- Main navbar -->
    @include('layouts.header')
    <!-- /main navbar -->


    <!-- Page container -->
    <div class="page-container">

        <!-- Page content -->
        <div class="page-content">

            <!-- Main sidebar -->
            @include('layouts.sidebar')
            <!-- /main sidebar -->

            <!-- Main content -->
            <div class="content-wrapper">



                <!-- Content area -->
                <div class="content">

                    @include('layouts.message')

                    @yield('content')


              

                </div>
                <!-- /content area -->

            </div>
            <!-- /main content -->

        </div>
        <!-- /page content -->

    </div>
    <!-- /page container -->

</body>

</html>
