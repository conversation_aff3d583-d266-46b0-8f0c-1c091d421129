@if(count($errors) > 0)
<div class="alert alert-danger alert-styled-left">
<button type="button" class="close" data-dismiss="alert"><span>&times;</span><span class="sr-only">Close</span></button>
    @foreach($errors->all() as $error)
            <p>{{$error}}</p>
    @endforeach
</div>  
@endif

@if(session('success'))
    <div class="alert alert-success alert-styled-left">
    <button type="button" class="close" data-dismiss="alert"><span>&times;</span><span class="sr-only">Close</span></button>
        {{session('success')}}
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-styled-left">
            <button type="button" class="close" data-dismiss="alert"><span>&times;</span><span class="sr-only">Close</span></button>
        {{session('error')}}
    </div>
@endif

@if(session('warning'))
    <div class="alert alert-warning alert-styled-left">
            <button type="button" class="close" data-dismiss="alert"><span>&times;</span><span class="sr-only">Close</span></button>
        {{session('warning')}}
    </div>
@endif