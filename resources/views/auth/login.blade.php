<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from demo.interface.club/limitless/layout_1/LTR/material/login_transparent.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 28 Nov 2017 18:06:41 GMT -->
<!-- Added by HTTrack --><meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->
<head>
	<meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="{{ asset ("assets/nautilus_logo.png")}}" type="image/gif" sizes="16x16">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Nautilus - Login</title>

	<!-- Global stylesheets -->
	<link href="../../../../../fonts.googleapis.com/css1381.css?family=Roboto:400,300,100,500,700,900" rel="stylesheet" type="text/css">
	<link href="assets/css/icons/icomoon/styles.css" rel="stylesheet" type="text/css">
	<link href="assets/css/bootstrap.css" rel="stylesheet" type="text/css">
	<link href="assets/css/core.css" rel="stylesheet" type="text/css">
	<link href="assets/css/components.css" rel="stylesheet" type="text/css">
	<link href="assets/css/colors.css" rel="stylesheet" type="text/css">
	<!-- /global stylesheets -->

	<!-- Core JS files -->
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/loaders/pace.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/core/libraries/jquery.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/core/libraries/bootstrap.min.js") }}"></script>
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/loaders/blockui.min.js") }}"></script>
	<!-- /core JS files -->

	<!-- Theme JS files -->
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/styling/uniform.min.js") }}"></script>
    <script type="text/javascript" src="{{ asset ("assets/js/core/app.js") }}"></script>
    <script type="text/javascript" src="{{ asset ("assets/js/plugins/ui/ripple.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/pages/login.js") }}"></script>
	<!-- /theme JS files -->

</head>

<body class="login-container" style="background-image: url({{ asset ("assets/login_bg.jpg")}})">

	<!-- Main navbar -->
	{{-- <div class="navbar navbar-inverse bg-indigo">
		<div class="navbar-header">
			<a class="navbar-brand" href="index.html"><img src="assets/images/logo_light.png" alt=""></a>

			<ul class="nav navbar-nav pull-right visible-xs-block">
				<li><a data-toggle="collapse" data-target="#navbar-mobile"><i class="icon-tree5"></i></a></li>
			</ul>
		</div>

		<div class="navbar-collapse collapse" id="navbar-mobile">
			<ul class="nav navbar-nav navbar-right">
				<li>
					<a href="#">
						<i class="icon-display4"></i> <span class="visible-xs-inline-block position-right"> Go to website</span>
					</a>
				</li>

				<li>
					<a href="#">
						<i class="icon-user-tie"></i> <span class="visible-xs-inline-block position-right"> Contact admin</span>
					</a>
				</li>

				<li class="dropdown">
					<a class="dropdown-toggle" data-toggle="dropdown">
						<i class="icon-cog3"></i>
						<span class="visible-xs-inline-block position-right"> Options</span>
					</a>
				</li>
			</ul>
		</div>
	</div> --}}
	<!-- /main navbar -->


	<!-- Page container -->
	<div class="page-container">

		<!-- Page content -->
		<div class="page-content">

			<!-- Main content -->
			<div class="content-wrapper">

				<!-- Content area -->
				<div class="content">

					<!-- Advanced login -->
                    <form action="{{ route('login') }}" method="POST">
                        @csrf
						<div class="login-form" style="border: 0px solid black;box-shadow: 15px 15px 10px  #4b4b4b, -2px -2px 10px  #888888;background-color: rgba(255, 255, 255, 0.1);padding: 10px;border-radius: 20px">
							<div class="text-center">
								<div class="icon-object " style="padding: 0px;border: 0px"><img src="{{ asset ("assets/nautilus_logo.png")}}" style="height: 80px"></div>
								<h5 class="content-group-lg">Login to your account <small class="display-block">Enter your credentials</small></h5>
							</div>
                            <div class="form-group has-feedback has-feedback-left">
                                <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email" autofocus>
								<div class="form-control-feedback">
									<i class="icon-user text-muted"></i>
                                </div>
                                @error('email')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>

							<div class="form-group has-feedback has-feedback-left">
								<input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" required autocomplete="current-password">
								<div class="form-control-feedback">
									<i class="icon-lock2 text-muted"></i>
                                </div>
                                @error('password')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
							</div>

							<div class="form-group login-options">
								<div class="row">
									<div class="col-sm-6">
										<label class="checkbox-inline">
											<input type="checkbox" class="styled" name="remember"  {{ old('remember') ? 'checked' : '' }}>
											{{ __('Remember Me') }}
										</label>
									</div>

									{{-- <div class="col-sm-6 text-right">
										<a href="login_password_recover.html">Forgot password?</a>
									</div> --}}
								</div>
							</div>

							<div class="form-group" >
								<button type="submit" style="border-radius: 30px;background-color: rgba(42, 44, 71, 1);color: white" class="btn bg-pink-400 btn-block btn-lg">{{ __('Login') }} <i class="icon-arrow-right14 position-right"></i></button>
							</div>

						</div>
					</form>
					<!-- /advanced login -->

				</div>
				<!-- /content area -->

			</div>
			<!-- /main content -->

		</div>
		<!-- /page content -->

	</div>
	<!-- /page container -->

</body>

<!-- Mirrored from demo.interface.club/limitless/layout_1/LTR/material/login_transparent.html by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 28 Nov 2017 18:06:41 GMT -->
</html>
