@extends('layouts.main')

@section('content')

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/redmond/jquery-ui.min.css">
{{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/ui-lightness/jquery-ui.min.css"> --}}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/css/ui.jqgrid.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/jquery.jqgrid.min.js"></script>



<style>

.ui-jqgrid{
    background-color: 'red' !important;
}
.ui-jqgrid .ui-jqgrid-htable th {
    height: 2em !important;
    font-size: 16px;
}
.ui-jqgrid tr.jqgrow td{
    height: 2.5em !important;
    font-size: 14px;
}

</style>

    <div class="panel panel-primary" id="add_div" hidden>
        <div class="panel-heading">
            <h5 class="panel-title">Add City</h5>
            <div class="heading-elements">
                
            </div>
        </div>
        <form action="" method="POST" enctype="multipart/form-data">
            @csrf 
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>City Name</h5></label>
                                    <div class="col-lg-9">
                                        <input type="text" name="city_name" class="form-control">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>State/Province</h5></label>
                                    <div class="col-lg-9">
                                        <select name="state_province" class="select" required>
                                            @foreach($states as $state)
                                                <option value="{{$state->id}}">{{$state->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Country Code</h5></label>
                                    <div class="col-lg-9">
                                        <select name="country_code"  class="select" required>
                                            @foreach($country_codes as $country_code)
                                                <option value="{{$country_code->country_code}}">{{$country_code->country_code}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>

    <div class="panel panel-primary" id="edit_div" hidden>
        <div class="panel-heading">
            <h5 class="panel-title">Edit City</h5>
            <div class="heading-elements">
                <form class="form-horizontal" id="delete_form" action="" method="POST" enctype="multipart/form-data">        
                    <input type="hidden" name="_method" value="DELETE">

                    <div class="text-right">
                        @csrf
                        <button type="submit" style="background-color: rgba(42, 44, 71, 1);color: white" class="btn btn-default">Delete<i class="icon-trash position-right"></i></button>
                    </div>
                </form> 
            </div>
        </div>
        <form action="" id="edit_form" method="POST" enctype="multipart/form-data">
            @csrf @method('put')
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>City Name</h5></label>
                                    <div class="col-lg-9">
                                        <input type="text" id="edit_city_name" class="form-control" name="city_name">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>State/Province</h5></label>
                                    <div class="col-lg-9">
                                        <select name="edit_state_province" class="select" id="edit_state_province" required>
                                            @foreach($states as $state)
                                                <option value="{{$state->id}}">{{$state->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Country Code</h5></label>
                                    <div class="col-lg-9">
                                        <select name="edit_country_code"  class="select" id="edit_country_code" required>
                                        
                                            @foreach($country_codes as $country_code)
                                                <option value="{{$country_code->country_code}}">{{$country_code->country_code}}</option>
                                            @endforeach
                                        
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </div>
        </form>
    </div>



<div class="panel panel-primary">
    <div class="panel-heading">
        <h5 class="panel-title">Cities</h5>
        <div class="heading-elements">
            {{-- <button ><i class="fa fa-plus"></i>&nbsp Add City</button> --}}
            <button class="btn btn-default" id="add_btn" style="background-color: rgba(42, 44, 71, 1);color: white"><i class="fa fa-plus"></i>&nbsp Add City</button>
        </div>
    </div>

    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
               
            <div id="jqGrid_container">
                <table id="list2"></table>
                <div id="pager2"></div>
            </div>
            </div>
            
        </div>
    </div>
</div>



<script>
$( document ).ready(function() {

    $('#add_btn').click(function() {
        $('#edit_div').hide(900);
        $('#add_div').show(900);
    });
    
    

    $("#list2").jqGrid({
   	url:'/all-cities',
	datatype: "json",
   	colNames:['ID','Name', 'State/Province','Country Code','Edit'],
   	colModel:[
   		{name:'id', index:'id', width:80},
        {name:'name', index:'name'},
        {name:'state_or_province.name', index:'state_or_province.name'},
        {name:'country_code', index:'country_code'},
        {name: 'state_or_province_id', template: 'actions',width:120,formatter:returnMyAction}
   				
   	],
   	rowNum:10,
    height: 'auto',
    autowidth: true,
    shrinkToFit: true,
   	rowList:[10,20,30],
   	pager: '#pager2',
    viewrecords: true,
    loadonce: true,
    rownumbers: true,
    searching: {
        defaultSearch: "cn"
    },    
    
    }).jqGrid("filterToolbar");


    function returnMyAction(cellValue, options, rowdata) 
    {
        // return '<button class="btn btn-info" id="add_btn"><i class="fa fa-edit"></i></button>';
        return "<a href='javascript:' style='font-size:28px' onclick='getId(\""+rowdata.name+","+rowdata.state_or_province_id+","+rowdata.country_code+","+rowdata.id+"\");'><i class='fas fa-edit'></i></a>";
    } 
    $(window).bind('resize', function() {
        var width = $('#jqGrid_container').width();
        $('#list2').setGridWidth(width);
    });
    $('#pager2').css({"height":"2em", "font-size":"14px"});
    $("#list2").jqGrid('navGrid','#pager2',{del:false,add:false,edit:false},{},{},{},{multipleSearch:true});

    $("#list2").jqGrid('navButtonAdd','#pager2',{
        id:'ExportToExcel',
        caption:'',
        title:'Export Selected Rows To CSV',
        onClickButton : function(e)
        {
            JSONToCSVConvertor(JSON.stringify($('#list2').jqGrid('getRowData')), 'Title', true);
        },
        buttonicon: 'ui-icon ui-icon-document',
    });


    function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {
        //If JSONData is not an object then JSON.parse will parse the JSON string in an Object
        var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
        
        var CSV = '';    
        //Set Report title in first row or line
        
        CSV += ReportTitle + '\r\n\n';

        //This condition will generate the Label/Header
        if (ShowLabel) {
            var row = "";
            
            //This loop will extract the label from 1st index of on array
            for (var index in arrData[0]) {
                
                //Now convert each value to string and comma-seprated
                row += index + ',';
            }

            row = row.slice(0, -1);
            
            //append Label row with line break
            CSV += row + '\r\n';
        }
        
        //1st loop is to extract each row
        for (var i = 0; i < arrData.length; i++) {
            var row = "";
            //2nd loop will extract each column and convert it in string comma-seprated
            for (var index in arrData[i]) {
                if(index == 'act'){
                    break;
                }
                row += '"' + arrData[i][index] + '",';
                
            }

            row.slice(0, row.length - 1);
            
            //add a line break after each row
            CSV += row + '\r\n';
        }

        if (CSV == '') {        
            alert("Invalid data");
            return;
        }   
        
        //Generate a file name
        var fileName = "MyReport_";
        //this will remove the blank-spaces from the title and replace it with an underscore
        fileName += ReportTitle.replace(/ /g,"_");   
        
        //Initialize file format you want csv or xls
        var uri = 'data:text/csv;charset=utf-8,' + escape(CSV);
        
        // Now the little tricky part.
        // you can use either>> window.open(uri);
        // but this will not work in some browsers
        // or you will not get the correct file extension    
        
        //this trick will generate a temp <a /> tag
        var link = document.createElement("a");    
        link.href = uri;
        
        //set the visibility hidden so it will not effect on your web-layout
        link.style = "visibility:hidden";
        link.download = fileName + ".csv";
        
        //this part will append the anchor tag and remove it after automatic click
        // console.log(document.body);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }



});
function getId(data){
    data = data.split(',');

    $('#edit_state_province').val(data[1]).change();
    $('#edit_country_code').val(data[2]).change();
    $('#edit_city_name').val(data[0]);
    $('#edit_form').attr('action','city/'+data[3]+'');
    $('#delete_form').attr('action','city/'+data[3]+'');
    $('#add_div').hide(900);
    $('#edit_div').show(900);
}
        

</script>
   




@endsection