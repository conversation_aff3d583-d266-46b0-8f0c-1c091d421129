@extends('layouts.main')

@section('content')

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/redmond/jquery-ui.min.css">
{{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/ui-lightness/jquery-ui.min.css"> --}}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/css/ui.jqgrid.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/jquery.jqgrid.min.js"></script>



<style>

.ui-jqgrid{
    background-color: 'red' !important;
}
.ui-jqgrid .ui-jqgrid-htable th {
    height: 2em !important;
    font-size: 16px;
    white-space: normal !important;
}
.ui-jqgrid tr.jqgrow td{
    height: 2.5em !important;
    font-size: 14px;
    white-space: normal !important;
}

</style>

    
<div class="panel panel-primary" >
    <div class="panel-heading">
        <h5 class="panel-title">Search Orders</h5>
        <div class="heading-elements">
            
        </div>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-lg-2 control-label"><h5>Enter Order IDs</h5></label>
                            <div class="col-lg-10">
                                <textarea type="text" name="order_ids" class="form-control" id="order_ids" style="width: 70%;height: 140px;"></textarea>
                                <br>
                                <p class="content-group-sm text-muted">Enter comma separated Order IDs.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-right">
            <button type="submit" class="btn btn-primary" id="submit_ids">Submit</button>
        </div>
    </div>
</div>



<div class="panel panel-primary">
    <div class="panel-heading">
        <h5 class="panel-title">Cities</h5>
        <div class="heading-elements">
            {{-- <button ><i class="fa fa-plus"></i>&nbsp Add City</button> --}}
            <button class="btn btn-default" id="add_btn" style="background-color: rgba(42, 44, 71, 1);color: white"><i class="fa fa-plus"></i>&nbsp Add City</button>
        </div>
    </div>

    <div class="panel-body">
        <div class="row">
            <div class="col-md-12">
               
            <div id="jqGrid_container">
                <table id="list2"></table>
                <div id="pager2"></div>
            </div>
            </div>
            
        </div>
    </div>
</div>



<script>
$( document ).ready(function() {

    var ids = '';
    // $("#order_ids").keyup(function(){
    //     // Getting the current value of textarea
    //     var currentText = $(this).val().replace(',', '\n');

  
        
    //     // Setting the Div content
    //     $("#order_ids").val(currentText);
    // });

    var count = 0;
    
    $('#submit_ids').click(function(){
        ids = $("#order_ids").val();
        if(ids == ''){
            alert('Enter atleast 1 Order ID');
        }
        else{
            if(count != 0){
                $("#list2").jqGrid().setGridParam({url : '/sync-report-data/'+ids}).trigger("reloadGrid");
            }
            else{
                count++;
                $("#list2").jqGrid({
                url:'/sync-report-data/'+ids,
                datatype: "json",
                colNames:['Order ID','Tracking Number', 'Courier','Last Scan Time','Last Tracking Status Received','Last Khaadi Magento Status Synced','Last Khaadi Magento Status Sync Date'],
                colModel:[
                    {name:'order_id', index:'order_id'},
                    {name:'tracking_id', index:'tracking_id'},
                    {name:'courier_name', index:'courier_name'},
                    {name:'last_run', index:'last_run'},
                    {name:'last_tracking_status', index:'last_tracking_status'},
                    {name:'last_magento_status', index:'last_magento_status'},
                    {name:'last_magento_status_date', index:'last_magento_status_date'},
                            
                ],
                rowNum:5000,
                height: 'auto',
                autowidth: true,
                shrinkToFit: true,
                rowList:[5000,10000,50000],
                pager: '#pager2',
                viewrecords: true,
                loadonce: false,
                rownumbers: true,
                searching: {
                    defaultSearch: "cn"
                },    
                
                }).jqGrid("filterToolbar");


                $(window).bind('resize', function() {
                    var width = $('#jqGrid_container').width();
                    $('#list2').setGridWidth(width);
                });
                $('#pager2').css({"height":"2em", "font-size":"14px"});
                $("#list2").jqGrid('navGrid','#pager2',{del:false,add:false,edit:false},{},{},{},{multipleSearch:true});

                $("#list2").jqGrid('navButtonAdd','#pager2',{
                    id:'ExportToExcel',
                    caption:'',
                    title:'Export Selected Rows To CSV',
                    onClickButton : function(e)
                    {
                        JSONToCSVConvertor(JSON.stringify($('#list2').jqGrid('getRowData')), 'Title', true);
                    },
                    buttonicon: 'ui-icon ui-icon-document',
                });


                function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {
                    //If JSONData is not an object then JSON.parse will parse the JSON string in an Object
                    var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
                    
                    var CSV = '';    
                    //Set Report title in first row or line
                    
                    CSV += ReportTitle + '\r\n\n';

                    //This condition will generate the Label/Header
                    if (ShowLabel) {
                        var row = "";
                        
                        //This loop will extract the label from 1st index of on array
                        for (var index in arrData[0]) {
                            
                            //Now convert each value to string and comma-seprated
                            row += index + ',';
                        }

                        row = row.slice(0, -1);
                        
                        //append Label row with line break
                        CSV += row + '\r\n';
                    }
                    
                    //1st loop is to extract each row
                    for (var i = 0; i < arrData.length; i++) {
                        var row = "";
                        //2nd loop will extract each column and convert it in string comma-seprated
                        for (var index in arrData[i]) {
                            if(index == 'act'){
                                break;
                            }
                            row += '"' + arrData[i][index] + '",';
                            
                        }

                        row.slice(0, row.length - 1);
                        
                        //add a line break after each row
                        CSV += row + '\r\n';
                    }

                    if (CSV == '') {        
                        alert("Invalid data");
                        return;
                    }   
                    
                    //Generate a file name
                    var fileName = "MyReport_";
                    //this will remove the blank-spaces from the title and replace it with an underscore
                    fileName += ReportTitle.replace(/ /g,"_");   
                    
                    //Initialize file format you want csv or xls
                    var uri = 'data:text/csv;charset=utf-8,' + escape(CSV);
                    
                    // Now the little tricky part.
                    // you can use either>> window.open(uri);
                    // but this will not work in some browsers
                    // or you will not get the correct file extension    
                    
                    //this trick will generate a temp <a /> tag
                    var link = document.createElement("a");    
                    link.href = uri;
                    
                    //set the visibility hidden so it will not effect on your web-layout
                    link.style = "visibility:hidden";
                    link.download = fileName + ".csv";
                    
                    //this part will append the anchor tag and remove it after automatic click
                    // console.log(document.body);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }
        }

        
      
        
            
        

        
    })

    
    



});

        

</script>
   




@endsection 