@extends('layouts.main')

@section('content')

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/redmond/jquery-ui.min.css">
{{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/ui-lightness/jquery-ui.min.css"> --}}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/css/ui.jqgrid.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/jquery.jqgrid.min.js"></script>



<style>

.ui-jqgrid{
    background-color: 'red' !important;
}
.ui-jqgrid .ui-jqgrid-htable th {
    height: 2em !important;
    font-size: 16px;
}
.ui-jqgrid tr.jqgrow td{
    height: 2.5em !important;
    font-size: 14px;
}

</style>

    <div class="panel panel-primary" id="search_div">
        <div class="panel-heading">
            <h5 class="panel-title">Get Shipment Tracking Details From Booking Partner Directly</h5>
        </div>
        <form action="/tracking/track-order" method="POST" enctype="multipart/form-data">
            @csrf 
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-lg-3 control-label"><h5>Shipment Tracking Number (CN)</h5></label>
                            <div class="col-lg-9">
                                <input type="text" name="tracking_number" class="form-control">
                            </div>
                            <span class="text-muted">Note: 1 Shipment at a time</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>


@if (isset($tracking_data))
    <div class="panel panel-primary">
        <div class="panel-heading">
            <h5 class="panel-title">{{$title}}</h5>
        </div>

        <div class="panel-body">
            <div class="col-md-6">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                            @foreach ($tracking_data as $key => $item)
                                
                                @if ($key != 'tracking_data')
                                    <tr>
                                        <td><b>{{$key}}</b></td>
                                        <td>{{$item}}</td>
                                    </tr>
                                @endif

                            @endforeach

                            @if (isset($tracking_data['tracking_data']))
                                <tr>
                                    <td style="text-align: center" colspan="2"><h3><b>Tracking Data</b></h3></td>
                                </tr>

                                @foreach ($tracking_data['tracking_data'] as $key => $item)
                                
                                    <tr>
                                        <td style="text-align: center" colspan="2"><b>Entry No : {{$key+1}}</b></td>
                                    </tr>

                                    @if (is_array($item))
                                        @foreach ($item as $value_key => $value)
                                            <tr>
                                                <td><b>{{$value_key}}</b></td>
                                                <td>{{$value}}</td>
                                            </tr>
                                        @endforeach
                                        
                                    @else
                                        <tr>
                                            <td colspan="2">{{$item}}</td>
                                        </tr>
                                    @endif

                                @endforeach

                            @endif
                            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

@endif




@endsection