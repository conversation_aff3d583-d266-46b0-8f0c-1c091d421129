@extends('layouts.main')

@section('content')

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/redmond/jquery-ui.min.css">
{{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.4/themes/ui-lightness/jquery-ui.min.css"> --}}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/css/ui.jqgrid.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/free-jqgrid/4.15.5/jquery.jqgrid.min.js"></script>

<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/datatables.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/buttons.min.js") }}"></script>
	<script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/selects/select2.min.js") }}"></script>



<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/col_reorder.min.js") }}"></script>
<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/select.min.js") }}"></script>
<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js") }}"></script>
<script type="text/javascript" src="{{ asset ("assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js") }}"></script>

<script type="text/javascript" src="{{ asset ("assets/js/plugins/forms/selects/select2.min.js") }}"></script>

<script type="text/javascript" src="{{ asset ("assets/js/plugins/pickers/daterangepicker.js") }}"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.5.3/jspdf.debug.js" integrity="sha384-NaWTHo/8YCBYJ59830LTz/P4aQZK1sS0SneOgAvhsIl3zBu8r9RevNg5lHCHAuQ/" crossorigin="anonymous"></script>

<style>

.ui-jqgrid{
    background-color: 'red' !important;
}
.ui-jqgrid .ui-jqgrid-htable th {
    height: 2em !important;
    font-size: 16px;
}
.ui-jqgrid tr.jqgrow td{
    height: 2.5em !important;
    font-size: 14px;
}
#shipment tbody tr {
        cursor: pointer;
    }

    #firstTable{
        margin: 0 auto;
        width: 100%;
        clear: both;
        border-collapse: collapse;
        table-layout: fixed;
        word-wrap:break-word;
    }

   
</style>

    <div class="panel panel-primary" id="add_div" hidden>
        <div class="panel-heading">
            <h5 class="panel-title">Add City</h5>
            <div class="heading-elements">
                
            </div>
        </div>
        <form action="" method="POST" enctype="multipart/form-data">
            @csrf 
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Courier</h5></label>
                                    <div class="col-lg-9">
                                        <select name="courier" class="select" required>
                                            @foreach($couriers as $courier)
                                                <option value="{{$courier->id}}">{{$courier->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Master City</h5></label>
                                    <div class="col-lg-9">
                                        <select name="master_city" data-placeholder="Select City" class="select-remote-data" required>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Courier City Code</h5></label>
                                    <div class="col-lg-9">
                                        <input type="text" name="courier_city_code" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Courier City Name</h5></label>
                                    <div class="col-lg-9">
                                        <input type="text" name="courier_city_name" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>

    <div class="panel panel-primary" id="edit_div" hidden>
        <div class="panel-heading">
            <h5 class="panel-title">Edit City</h5>
            <div class="heading-elements">
                <form class="form-horizontal" id="delete_form" action="" method="POST" enctype="multipart/form-data">        
                    <input type="hidden" name="_method" value="DELETE">

                    <div class="text-right">
                        @csrf
                        <button type="submit" style="background-color: rgba(42, 44, 71, 1);color: white" class="btn btn-default">Delete<i class="icon-trash position-right"></i></button>
                    </div>
                </form> 
            </div>
        </div>
        <form action="" id="edit_form" method="POST" enctype="multipart/form-data">
            @csrf @method('put')
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Courier</h5></label>
                                    <div class="col-lg-9">
                                        <select name="edit_courier" id="edit_courier" class="select" required>
                                            @foreach($couriers as $courier)
                                                <option value="{{$courier->id}}">{{$courier->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Master City</h5></label>
                                    <div class="col-lg-9">
                                        <select name="edit_master_city" id="edit_master_city" data-placeholder="Select City" class="select-remote-data" required>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Courier City Code</h5></label>
                                    <div class="col-lg-9">
                                        <input type="text" name="edit_courier_city_code" id="edit_courier_city_code" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label"><h5>Courier City Name</h5></label>
                                    <div class="col-lg-9">
                                        <input type="text" name="edit_courier_city_name" id="edit_courier_city_name" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </div>
        </form>
    </div>



<div class="panel panel-primary">
    <div class="panel-heading">
        <h5 class="panel-title">Courier Cities</h5>
        <div class="heading-elements">
            {{-- <button ><i class="fa fa-plus"></i>&nbsp Add City</button> --}}
            <button class="btn btn-default" id="add_btn" style="background-color: rgba(42, 44, 71, 1);color: white"><i class="fa fa-plus"></i>&nbsp Add City</button>
        </div>
    </div>

    

    <div class="panel panel-info" id="dispatchedTable">
        
        <div class="panel-body">
            <div class='row'>
                <div class="col-md-12">
                    <table class="table" id="firstTable">
                        <thead>
                        <tr>
                            <th>Courier</th>
                            <th>Master city</th>
                            <th>Courier City Code</th>
                            <th>Courier City name</th>
                            <th>Edit</th>
                        </tr>
                        </thead>
                        <tfoot style="display: table-row-group;">
                            <tr>
                                <td>Courier</td>
                                <td>Master city</td>
                                <td>Courier City Code</td>
                                <td>Courier City name</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
$( document ).ready(function() {

    $(".select-remote-data").select2({
        ajax: {
            url: "/cities/search",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    string: params.term, // search term
                };
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        return {
                            text: item.name+" | "+item.country_code,
                            id: item.id
                        }
                    })
                };
            },
            cache: true
        },
        minimumInputLength: 1
    });

    $('#add_btn').click(function() {
        $('#edit_div').hide(900);
        $('#add_div').show(900);
    });
    
    



});
function getId(data){
    data = data.split(',');
    console.log(data);
    $('#edit_courier').val(data[0]).change();
    $('#edit_master_city').append($("<option></option>")
                    .attr("value",data[1])
                    .text(data[2])); 
                    
    // $('#edit_master_city').val(data[1]).change();
    $('#edit_courier_city_code').val(data[3]);
    $('#edit_courier_city_name').val(data[4]);
    $('#edit_form').attr('action','courier-city/'+data[5]+'');
    $('#delete_form').attr('action','courier-city/'+data[5]+'');
    $('#add_div').hide(900);
    $('#edit_div').show(900);
}


$.extend( $.fn.dataTable.defaults, {
            dom: '<"datatable-header"B><"datatable-scroll-wrap"tr><"datatable-footer"ip>',
            buttons: {
                buttons: [
                    {extend: 'pageLength',className: 'btn bg-slate-600'},
                    {
                        extend: 'collection',
                        text: 'Export',
                        className: 'btn bg-indigo-400 btn-icon',
                        buttons: [
                            {extend: 'copyHtml5',text: '<i class="icon-copy3"></i>&nbsp &nbsp Copy ',className: 'btn btn-default',
                                exportOptions: {
                                    columns: ':visible'
                                }
                            },
                            {extend: 'csvHtml5',text: '<i class="icon-file-excel"></i>&nbsp &nbsp CSV ',className: 'btn btn-default',
                                exportOptions: {
                                    columns: ':visible'
                                }
                            },
                            {extend: 'pdfHtml5',text: '<i class="icon-printer2"></i>&nbsp &nbsp pdf ',className: 'btn btn-default',
                                exportOptions: {
                                    columns: ':visible'
                                }
                            }
                        ]
                    },
                    {extend: 'colvis',columns: ':gt(1)',text: '<i class="icon-grid3"></i> <span class="caret"></span>',className: 'btn bg-indigo-400 btn-icon'}
                ]
            },
            select: {
                style: 'os',
                selector: 'td:first-child'
            },
            colReorder: true,
            stateSave: true,
            scrollX: true,
            scrollY: '50vh',
            scrollCollapse: true,
            "deferLoading": true,
            "processing": true,
            "language": {"processing": '<i style="color:green;font-size:50px" class="icon-spinner4 spinner"></i>'},

        });





        

/////////For Pending Table///////////



// Individual column searching with text inputs
            $('#firstTable tfoot td').each(function () {
                var title = $(this).text();
                if (!$(this).attr('id') && title) {
                    $(this).html('<input type="text" class="form-control input-sm" placeholder="Search '+title+'" />');
                }
            });

            var firstTable = $('#firstTable').DataTable( {
                lengthMenu: [
                    [ 100, 500, 1000, 5000, -1],
                    [ '100 rows', '500 rows', '1000 rows', '5000 rows' , 'All rows (Not Recommended)']
                ],
                
                "serverSide": true,
                "ajax": "/all-courier-cities",
                columns: [
                    { data: 'courier.name', name: 'courier.name'},
                    { data: 'city.name', name: 'city.name'},
                    { data: 'courier_city_code', name: 'courier_city_code' },
                    { data: 'courier_city_name', name: 'courier_city_name' },
                    { data: 'city.name', name: 'city.name' ,render: function ( data, type, row) {
                        return "<a href='javascript:' style='font-size:28px' onclick='getId(\""+row.courier_id+","+row.city_id+","+data+","+row.courier_city_code+","+row.courier_city_name+","+row.id+"\");'><i class='fas fa-edit'></i></a>";
                    } }
                    
                ],
                order: [[0, 'desc']],
                
            });

            firstTable.columns().search( '' ).draw();


            firstTable.columns().every( function () {
                var that = this;
                $('input', this.footer()).on('keyup change', function (e) {
                    if (e.keyCode == 13 || $(this).attr('id')) {
                        if ($(this).attr('id')) {
                            setTimeout(() => {
                                if (orderPlacedSearch) {
                                    var start = new Date (((this.value).split('-'))[0]);
                                    var end = new Date (((this.value).split('-'))[1]);
                                    var dateArray = new Array();
                                    while (start <= end) {
                                        start = start.addDays(1);
                                        dateArray.push((new Date (start)).toISOString().slice(0,10));
                                    }
                                    var range = dateArray.join('|');
                                    that.search(range,true,false).draw();

                                } else if(select1){
                                    that.search($('.select').val(),true,false).draw();
                                } else {
                                    that.search('',true,false).draw();
                                }
                                
                            }, 500);

                        } else {
                            that.search(((this.value).trim()).replace(/\|$/, ''),true,false).draw();
                        }
                    }
                });

                $('.select', this.footer()).on('change', function (e) {
                    if (e.keyCode == 13 || $(this).attr('id')) {
                        if ($(this).attr('id')) {
                            setTimeout(() => {
                                
                                that.search(this.value,true,false).draw();
                            }, 500);
                        }
                    }
                });
            });

          


          


        

</script>
   




@endsection