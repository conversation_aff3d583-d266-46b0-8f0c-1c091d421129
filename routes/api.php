<?php

use App\Helpers\TrackingRequest;
use App\Models\Shipment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});


Route::post('track/bykea', function(Request $request) {
    
    \Log::info(json_encode($request->all()));

    if (!isset($request->event)) {
        return response()->json(['message' => 'Event not found'], 400);
    } elseif (!isset($request->event_time)) {
        return response()->json(['message' => 'Event Time not found'], 400);
    } elseif (!isset($request->data)) {
        return response()->json(['message' => 'Data not found'], 400);
    }

    \Log::info('success');

    return TrackingRequest::bykea($request->all());
});



Route::post('track/quiqup', function(Request $request) {
    
    dd('Not Found');
    \Log::info($request);
    // $auth_sig = hash_hmac('sha1', $request->all(), 'yoitsasecretkey');
    // return $auth_sig;
    $count = 0;
    if (!$request->header('X-Signature')) {

        Mail::raw('Quiqup Webhook | Signature Required', function ($m)  {
            $m->to('<EMAIL>')->subject('Quiqup Tracking');
        });
        return response()->json(['message' => 'Signature Required'], 404);
        
    } elseif (!isset($request['payload'])) {
        Mail::raw('Quiqup Webhook | Payload Parameter not found', function ($m)  {
            $m->to('<EMAIL>')->subject('Quiqup Tracking');
        });
        return response()->json(['message' => 'Payload Parameter not found'], 404);
        
    } elseif (!isset($request['action'])) {
        Mail::raw('Quiqup Webhook | Action Parameter not found', function ($m)  {
            $m->to('<EMAIL>')->subject('Quiqup Tracking');
        });
        return response()->json(['message' => 'Action Parameter not found'], 404);
        
    } elseif (!isset($request['payload']['id'])) {
        Mail::raw('Quiqup Webhook | ID not found', function ($m)  {
            $m->to('<EMAIL>')->subject('Quiqup Tracking');
        });
        return response()->json(['message' => 'ID not found'], 404);
        
    } elseif (!isset($request['payload']['state'])) {
        Mail::raw('Quiqup Webhook | State not found', function ($m)  {
            $m->to('<EMAIL>')->subject('Quiqup Tracking');
        });
        return response()->json(['message' => 'State not found'], 404);
        
    }


    \Log::info('success');

    return TrackingRequest::quiqup($request);
});

Route::post('track/pandago', function(Request $request) {
    
    

    \Log::info('success');
    // return 1;
    return TrackingRequest::pandago($request);
})->middleware('pandago_webhook_auth');


Route::post('force-track','ShipmentController@forceTrack')->middleware('api_authenticate');
// Route::post('force-track-multi','ShipmentController@forceTrackMultiple');
