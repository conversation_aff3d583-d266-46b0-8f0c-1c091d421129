<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/', function () {
//     return view('welcome');
// });

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Auth::routes();

Route::get('/home', 'HomeController@index')->name('home');

Route::get('/test', 'ShipmentController@index')->name('home');

Route::get('logout', 'LoginController@logout');

Route::group(['prefix' => 'cron'], function() {

	// Route::get('shipment/track', 'ShipmentController@track');
	Route::get('shipment/specific_jobs/{id}', 'ShipmentController@specificJobs');
	Route::get('shipment/jobs', 'ShipmentController@jobs');
	Route::get('shipment/khaadi/jobs', 'ShipmentController@khaadi_jobs');
	Route::get('shipment/universal/jobs', 'ShipmentController@universal_tracking');
	Route::get('shipment/terminal/track', 'ShipmentController@track_terminal');
	Route::get('courier-exception/report', 'ReportsController@courierException');
	Route::get('shipment/track_payment/{id}', 'ShipmentController@track_payment');

	Route::resource('city','CityController');
	
});

// Master City
Route::get('/cities/search', 'CityController@search');
Route::get('/all-cities','CityController@allCities');
Route::resource('city','CityController');


// Courier City
Route::get('/all-courier-cities','CourierCityController@allCourierCities');
Route::resource('courier-city','CourierCityController');


// Khaadi Sync Report
Route::get('/sync-report','ReportsController@syncReportHome');
Route::get('/sync-report-data/{id}','ReportsController@syncReportShow');


// Tracking with Nautilus
Route::post('/force-track/run','TrackingController@forceTrackingRun');
Route::get('/force-track','TrackingController@forceTrackingPage');
Route::post('/tracking/track-order', 'TrackingController@trackOrder');
Route::get('/tracking', 'TrackingController@index');


// Temporary Monitoring Code
Route::get('/check-for-duplicates','TemporaryController@checkForDuplicates');
Route::get('/check-for-order-duplicates','TemporaryController@checkForOrderDuplicates');
Route::get('/check-for-khaadi-duplicates','TemporaryController@checkForKhaadiDuplicates');
Route::get('/check-for-not-booked-location-orders','TemporaryController@checkForNotBookedLocationOrders');
Route::get('/check-for-universal-shipment-exceptions/{hours}','TemporaryController@checkForUniversalShipmentExceptions');
Route::get('/check-for-uncommited-qty-greater-than-qty/{seller_id}','TemporaryController@checkForUncommitedQtyGreaterThanQty');
Route::get('/compare_jdot_shipment_cod_and_order_cod','TemporaryController@compareJdotShipmentCodAndOrdersCod');

// Route::get('/tes','ShipmentController@FunctionName');