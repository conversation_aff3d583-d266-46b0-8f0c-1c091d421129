{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "fideloper/proxy": "^4.0", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^8", "laravel/horizon": "^5.7", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "nesbot/carbon": "^2.38", "predis/predis": "^1.1", "spatie/laravel-activitylog": "^3.14", "vyuldashev/xml-to-array": "^1.0", "yajra/laravel-datatables": "^1.5"}, "require-dev": {"facade/ignition": "^2.3.6", "mockery/mockery": "^1.0", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["bootstrap/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}