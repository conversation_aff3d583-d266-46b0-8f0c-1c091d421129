<?php 

namespace App\Service;

use App\Events\KhaadiShipmentTrackingEvent;
use App\Events\ShipmentTrackingEvent;
use App\Events\ShopifyMarkPaidEvent;
use App\Helpers\StorefrontComment;
use App\Models\CODPayment;
use App\Models\CODPaymentShipment;
use App\Models\Order;
use App\Models\OrderComments;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentCharges;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Exception;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

use App\Events\BlueExTrackingEvent;
use App\Events\CallCourierTrackingEvent;
use App\Events\DaewooTrackingEvent;
use App\Events\LCSMerchantTrackingEvent;
use App\Events\LcsPaymentEvent;
use App\Events\LCSTrackingEvent;
use App\Events\MnpTrackingEvent;
use App\Events\PostExTrackingEvent;
use App\Events\TCSTrackingEvent;
use App\Events\TPLRiderTrackingEvent;
use App\Events\TraxTrackingEvent;

class TrackPayment
{
    private $shipment = null;
    private $client = null;

    public function __construct($shipment) {
        $this->shipment = $shipment;

        $this->client = new Client([
            'timeout' => 60, // Response timeout
            'connect_timeout' => 30, // Connection timeout
        ]);
    }


    public function callCourierFunction($name, $parameters = [])
    {
        $error = 0;

        try {

            return $this->$name($parameters);

        } catch(RequestException $e) {

            $error = 1;
            $message = 'Request Exception : ';

            if (method_exists($e, 'getResponse')) {

                if ($e->getResponse()) {

                    $error_response = json_decode(((string) $e->getResponse()->getBody()), true);

                    if ( isset($error_response['error']['message']) ) {
                        $message .= $error_response['error']['message'];
                    } elseif ( isset($error_response['message']) ) {
                        $message .= json_encode($error_response['message']);
                    } elseif ( isset($error_response['error']) ) {
                        $message .= json_encode($error_response['error']);
                    } elseif ( isset($error_response['errors']) ) {
                        $message .= json_encode($error_response['errors']);
                    } elseif ( isset($error_response['Status']) || isset($error_response['InstrumentMode']) ) {
                        $error = 2;
                        $message .= 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received by Courier '.$name.' | '.json_encode($error_response);
                    } elseif ( isset($error_response['BillingMethod']) ) {
                        $error = 2;
                        $message .= 'Shipment #'.$this->shipment->tracking_number.' invoice details has not Received by Courier '.$name.' | '.json_encode($error_response);
                    } elseif ( isset($error_response[0]['message']) ) {
                        $error = 2;
                        $message .= 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received by Courier '.$name.' | '.json_encode($error_response);
                    } else {
                        $message .= $e->getMessage();
                    }

                } else {
                    $message .= 'Get response message is null | '.$e->getMessage();
                }

            } else {
                $message .= 'Get response message is null | '.$e->getMessage();
            }

            if ($error == 1) {
                $activity_id = activity()
                ->performedOn($this->shipment)
                ->withProperties(['message' => $message, 'trace' => $e->getTraceAsString()])
                ->log( ucwords($name.' Payment Api') );
            }
        } catch(Exception $e) {
            $error = 1;
            $message = 'Exception : '.$e->getMessage();

            $activity_id = activity()
            ->performedOn($this->shipment)
            ->withProperties(['message' => $message, 'trace' => $e->getTraceAsString()])
            ->log( ucwords($name.' Payment Api') );
        }

        return compact('error', 'message');
    }


    public function updateShipment($payment_date, $courier_name)
    {
        Shipment::on('mysql3')->where('id',$this->shipment->id)->update(['cod_received' => 1, 'cod_received_at' => $payment_date]);
        OrderComments::add($this->shipment->order_id, 'COD Reconciliation Process', 'Shipment #'.$this->shipment->tracking_number.' COD amount has been <b> Received</b> Courier <b>'.$courier_name.'</b>', 'Success', '1' );
        
        // shopify mark paid
        if(Setting::whereSellerId($this->shipment->seller_id)->where('key','channel')->where('value','shopify')->exists() && Setting::whereSellerId($this->shipment->seller_id)->where('key','orderMarkPaid')->where('value','1')->exists()){
            event(new ShopifyMarkPaidEvent($this->shipment->order_id));
        }

        if ($this->shipment->seller_id == 119) {
            $order = Order::whereId($this->shipment->order_id)->first(['entity_id']);
            StorefrontComment::add($this->shipment->order_id, $order->entity_id, 'complete_instore', 'Payment settled by '.$courier_name);
        }
    }

    public function saveCODPayment($courier_id, $instrument_number, $instrument_mode, $payment_id, $receipt_amount)
    {
        $cod_payment = CODPayment::where('instrument_number',$instrument_number)->where('seller_id',$this->shipment->seller_id)->where('courier_id',$courier_id)->first();

        if( $cod_payment ) {
            // ignore
        } else {
            $cod_payment = new CODPayment();
            $cod_payment->instrument_number = $instrument_number;

            if($instrument_mode == 'IBFT') {
                $method = 3;
            } elseif($instrument_mode == 'Cash' || $instrument_mode == 'CASH') {
                $method = 1;
            } elseif($instrument_mode == 'Cheque' || $instrument_mode == 'CHEQUE' || $instrument_mode == 'Bank' || $instrument_mode == 'cheque') {
                $method = 2;
            }

            $cod_payment->method = $method;
            $cod_payment->amount = 0;
            $cod_payment->status = 'Processing';
            $cod_payment->seller_id = $this->shipment->seller_id;
            $cod_payment->courier_id = $courier_id;
            $cod_payment->save();
        }


        if (!CODPaymentShipment::where('shipment_id',$this->shipment->id)->where('cod_payments_id',$cod_payment->id)->count()) {
            $cod_payment_shipment = new CODPaymentShipment();
            $cod_payment_shipment->shipment_id = $this->shipment->id;
            $cod_payment_shipment->cod_payments_id = $cod_payment->id;
            $cod_payment_shipment->payment_reference_id = $payment_id;
            $cod_payment_shipment->save();

            $cod_payment->amount += $receipt_amount;
            $cod_payment->save();
        }

        
    }

    public function addShipmentCharges($invoice_number, $gst, $net_total, $gross_total, $weight_charges = 0, $delivery_charges = 0, $cash_handling_charges = 0, $fuel_adjustment_charges = 0)
    {
        $shipment_charges = ShipmentCharges::where('shipment_id',$this->shipment->id)->where('invoice_number',$invoice_number)->first(); 
        
        if(!$shipment_charges){
            $shipment_charges = new ShipmentCharges;
            $shipment_charges->shipment_id = $this->shipment->id;
            $shipment_charges->invoice_number = $invoice_number;
        }
            
        if ($weight_charges) $shipment_charges->weight_charges = $weight_charges;
        if ($delivery_charges) $shipment_charges->delivery_charges = $delivery_charges;
        if ($cash_handling_charges) $shipment_charges->cash_handling_charges = $cash_handling_charges;
        if ($fuel_adjustment_charges) $shipment_charges->fuel_adjustment_charges = $fuel_adjustment_charges;
        
        $shipment_charges->sales_tax = $gst;
        $shipment_charges->net_total = $net_total;
        $shipment_charges->gross_total = $gross_total;
        $shipment_charges->save();
    }

    /// Sometimes needed for FORCE TRACK Shipment 
    private function forceTrackStatus($message, $courier_name)
    {
        $enterprise = in_array($this->shipment->seller_id,[119,120]) ? 1 : 0;
        $shipment = $this->shipment->id;
        $type = 2;
        $courier_id = $this->shipment->courier_id;

        if ($enterprise == 0) {
            Log::info("addShipmentChargess >>> ".$this->shipment->id);
            Log::info("addShipmentChargess >>> Courier_id".$courier_id);

            if($courier_id == 5){
                event(new LCSTrackingEvent($shipment, $type));
            }if($courier_id == 35){
                event(new LCSMerchantTrackingEvent($shipment, $type));
            }if($courier_id == 13){
                event(new TCSTrackingEvent($shipment, $type));
            }if($courier_id == 10){
                event(new BlueExTrackingEvent($shipment, $type));
            }if($courier_id == 7){
                event(new CallCourierTrackingEvent($shipment, $type));
            }if($courier_id == 11){
                event(new TPLRiderTrackingEvent($shipment, $type));
            }if($courier_id == 4){
                event(new MnpTrackingEvent($shipment, $type));
            }if($courier_id == 28){
                event(new PostExTrackingEvent($shipment, $type));
            }if($courier_id == 8){
                event(new TraxTrackingEvent($shipment, $type));
            }if($courier_id == 34){
                event(new DaewooTrackingEvent($shipment, $type));
            }else{
                event(new ShipmentTrackingEvent($shipment, $type));
            }
            // event(new ShipmentTrackingEvent([$this->shipment->id], 2));
        } elseif ($enterprise == 1) {
            event(new KhaadiShipmentTrackingEvent([$this->shipment->id], 2));
        }
        OrderComments::add($this->shipment->order_id, 'COD Reconciliation Process', 'Delivered Shipment #'.$this->shipment->tracking_number.' '.$message.' <b>'.$courier_name.'</b> , triggering <b>Force Tracking</b> on this', 'Success', '1' );
    }

    public function rider($parameters)
    {
        $login_id = $parameters['login_id'];
        $api_key = $parameters['api_key'];

        $data = [   
            "CNNo" => $this->shipment->tracking_number,
            "loginId" => $login_id,
            "apikey" => $api_key
        ];

        $response = $this->client->post('http://api.withrider.com/rider/v1/getpaymentdetails', [
            'headers' => [ 'Content-Type' => 'application/json' ],
            'body' => json_encode($data)
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);

        if (isset($response) && isset($response['Status']) && $response['Status'] == 'Paid') {

            $this->updateShipment(Carbon::create($response['PaymentDate'])->toDateTimeString(), 'Rider');

            $error = 0;
            $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier Rider';

            $invoice_number = (isset($response['InvoiceNumber']) ? ($response['InvoiceNumber'] ? $response['InvoiceNumber'] : NULL) : NULL);

            if (isset($response['InstrumentNumber']) && isset($response['PaymentMethod']) ) {
                $this->saveCODPayment(11, $response['InstrumentNumber'], $response['PaymentMethod'], $invoice_number, 0);
            }





            // Getting invoice details
            if ($invoice_number) {

                $data = [   
                    "invNum" => $invoice_number,
                    "loginId" => $login_id,
                    "apikey" => $api_key
                ];

                $response = $this->client->post('http://api.withrider.com/rider/v1/getinvoicedetails', [
                    'headers' => [ 'Content-Type' => 'application/json' ],
                    'body' => json_encode($data)
                ]); // Request Send
        
                $response = $response->getBody()->getContents(); // Request Response
                $response = json_decode($response, TRUE);

                if ($response && is_array($response)) {
                    
                    foreach ($response as $key => $value) {
                        $this->addShipmentCharges( $invoice_number, $value['Tax'] , $value['NetTotal'], $value['GrossTotal'], $value['WeightCharged'], $value['DeliveryCharges'], $value['CashHandlingCharges'], $value['FuelAdjustmentCharges'] );
                    }
                }

            } else {
                $message .= ' | Invoice number not exists, so details can\'t be fetched';
            }


            
            
        } else {
            $error = 2;
            $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier Rider';
        }
        return compact('error', 'message');
    }



    public function mnp($parameters)
    {
        $username = $parameters['username'];
        $password = $parameters['password'];
        $account_no = $parameters['account_no'];

        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier MnP, shipment has unpaid status';

        $data = [   
            "consignmentNumber" => $this->shipment->tracking_number,
            "UserName" => $username,
            "Password" => $password,
            "AccountNumber" => $account_no
        ];

        $response = $this->client->post('http://mnpcourier.com/mycodapi/api/Reports/Payment_Report_Detail_CNWise', [
            'headers' => [ 'Content-Type' => 'application/json', 'Accept' => 'application/json' ],
            'body' => json_encode($data)
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);

        if ($response && $response != "Invalid Credentials") {

            Log::info("MnP Shipment #".$this->shipment->tracking_number." | RRSTATUS is ".(isset($response['RRSTATUS']) ? $response['RRSTATUS'] : "Not found")." | RRPayable is ".(isset($response['RRPayable']) ? $response['RRPayable'] : "Not found") );

            if (isset($response['RRSTATUS']) && strpos($response['RRSTATUS'], 'Return') !== false ) {
                $activity_id = activity()
                    ->performedOn($this->shipment)
                    ->withProperties(['message' => 'status is return' , 'dump' => $response])
                    ->log('MnP Payment Api Status Issue');

                $this->forceTrackStatus('tracking status in payment details is '.$response['RRSTATUS'].' by courier', 'MnP');
            }
            
            if (isset($response['RRPayable']) && $response['RRPayable'] == 'PAID') {

                $this->updateShipment(Carbon::create($response['paidon'])->toDateTimeString(), 'MnP');

                if (isset($response['InstrumentNumber']) && isset($response['InstrumentMode']) && isset($response['PaymentID']) && isset($response['ReceiptAmount']) ) {
                    $this->saveCODPayment(4, $response['InstrumentNumber'], $response['InstrumentMode'], $response['PaymentID'], $response['ReceiptAmount']);
                }

                $error = 0;
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier MnP';

                $response = $this->client->post('http://mnpcourier.com/mycodapi/api/Reports/Invoice_Report', [
                    'headers' => [ 'Content-Type' => 'application/json', 'Accept' => 'application/json' ],
                    'body' => json_encode($data)
                ]); // Request Send
        
                $response = $response->getBody()->getContents(); // Request Response
                $response = json_decode($response, TRUE);

                if(isset($response[0]['isSuccess']) && $response[0]['isSuccess'] == true && isset($response[0]['InvoiceNumber']) && isset($response[0]['GST'])) {
                    $this->addShipmentCharges( $response[0]['InvoiceNumber'], $response[0]['GST'] , (isset($response[0]['TotalAmount']) ? $response[0]['TotalAmount'] : 0), (isset($response[0]['TotalAmount']) ? $response[0]['TotalAmount'] : 0) );
                } else {
                    $message .= ' | Invoice failed';
                }
            }
        }

        return compact('error', 'message');
    }


    public function lcs($parameters)
    {
        $api_key = $parameters['api_key'];
        $api_password = $parameters['api_password'];

        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier LCS, shipment has unpaid status';

        $response = $this->client->post('http://new.leopardscod.com/webservice/trackBookedPacket/format/json', [
            'form_params' => [
                'api_key' => $api_key,
                'api_password' => $api_password,
                'track_numbers' => $this->shipment->tracking_number
            ]
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);

        if ($response && isset($response['status']) ) {

            Log::info("LCS Shipment #".$this->shipment->tracking_number." | booked_packet_status is ".(isset($response['packet_list'][0]['booked_packet_status']) ? $response['packet_list'][0]['booked_packet_status'] : "Not found") );
            
            if (isset($response['packet_list'][0]['booked_packet_status']) && $response['packet_list'][0]['booked_packet_status'] != 'Delivered' ) {
                $activity_id = activity()
                    ->performedOn($this->shipment)
                    ->withProperties(['message' => 'status is not Delivered' , 'dump' => $response])
                    ->log('LCS Payment Api Status Issue');

                $this->forceTrackStatus('tracking status in payment details is not delivered by courier', 'LCS');
            }

            if ($response['status'] == 1 && isset($response['packet_list'][0]['invoice_number']) && $response['packet_list'][0]['invoice_number'] != '') {

                $payment_date = Carbon::create($response['packet_list'][0]['invoice_date'])->toDateTimeString();
                $this->updateShipment($payment_date, 'LCS');

                $error = 0;
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier LCS';
            } else {
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier LCS';
            }

        }

        return compact('error', 'message');
    }

    public function lcs_merchant($parameters)
    {
        $api_key = $parameters['api_key'];
        $api_password = $parameters['api_password'];

        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier LCS_Merchant, shipment has unpaid status';

        $response = $this->client->get('https://merchantapi.leopardscourier.com/api/getPaymentDetails/format/json/?api_key='.$api_key.'&api_password='.$api_password.'&cn_numbers='.$this->shipment->tracking_number);

        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);

        if ($response && isset($response['status']) ) {

            Log::info("LCS Shipment #".$this->shipment->tracking_number." | booked_packet_cn is ".(isset($response['payment_list'][0]['booked_packet_cn']) ? $response['payment_list'][0]['booked_packet_cn'] : "Not found") );
            
            if ($response['status'] == 1 && isset($response['payment_list'][0]['invoice_cheque_no']) && $response['payment_list'][0]['invoice_cheque_no'] != '') {

                $payment_date = Carbon::create($response['payment_list'][0]['invoice_cheque_date'])->toDateTimeString();
                $this->updateShipment($payment_date, 'LCS Merchant');

                $error = 0;
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier LCS Merchant';
            } else {
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier LCS Merchant';
            }

        }

        return compact('error', 'message');
    }


    public function call_courier($parameters)
    {
        $username = $parameters['username'];
        $password = $parameters['password'];
        $account_id = $parameters['account_id'];

        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier Call Courier, shipment has unpaid status';
        $d1 = [
            'userName' => $username,
            'password' => $password
        ];

        $response1 = $this->client->post('http://cod.callcourier.com.pk/api/callcourier/authForFinancials', [
            'headers' => [ 'Content-Type' => 'application/json'],
            'body' => json_encode($d1)

        ]); // Request Send

        $response1 = $response1->getBody()->getContents(); // Request Response
        $response1 = json_decode($response1, TRUE);
        
        if ($response1 && isset($response1['Response']) && $response1['Response'] == true && isset($response1['Token']) ) {
          

            $d2 = [
                'CNs' => $this->shipment->tracking_number,
                'AccountID' => $account_id,
                'Token' => $response1['Token']
            ];
            
            $response = $this->client->post('http://cod.callcourier.com.pk/api/callcourier/getFinancials', [
                'headers' => [ 'Content-Type' => 'application/json'],
                'body' => json_encode($d2)
            ]); // Request Send
            
            $response = $response->getBody()->getContents(); // Request Response
            $response = json_decode($response, TRUE);
            
            if (isset($response) ) {
              
                if (isset($response['ResponseForGetFinancialObj'][0]['CODAmount_Settelment']) && $response['ResponseForGetFinancialObj'][0]['CODAmount_Settelment'] != '') {
                  
                    $payment_date = $response['ResponseForGetFinancialObj'][0]['InvoiceDate'];
                    $this->updateShipment($payment_date, 'Call Courier');
                    $error = 0;
                    $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier Call Courier';
                    $this->saveCODPayment(7, $response['ResponseForGetFinancialObj'][0]['InvoiceNo'], 'IBFT', $response['ResponseForGetFinancialObj'][0]['InvoiceID'], 0);
                   
                } else {
                    $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier Call Courier';
                }
    
            }

        }

        return compact('error', 'message');
    }


    public function trax($parameters)
    {
        $authorization_key = $parameters['authorization_key'];

        $allowed = ['Paid'];
        $ignore = ['Processed','Charges - Deducted','Reverted'];
        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier Trax';

        $data = [   
            "tracking_number" => [ $this->shipment->tracking_number ]
        ];

        $response = $this->client->post('https://sonic.pk/api/payments', [
            'headers' => [ 'Content-Type' => 'application/json', 'Accept' => 'application/json', 'Authorization' => $authorization_key ],
            'body' => json_encode($data)
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);

        if (isset($response['status']) && $response['status'] == 0) {

            if (isset($response['payments'][$this->shipment->tracking_number][0]['payment_status']) ) {

                if (in_array($response['payments'][$this->shipment->tracking_number][0]['payment_status'], $allowed)) {
                
                    $payment_date = $response['payments'][$this->shipment->tracking_number][0]['payment_date'];
                    $this->updateShipment($payment_date, 'Trax');
                    $error = 0;
                    $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier Trax';
                    $this->saveCODPayment(8, 0, $response['payments'][$this->shipment->tracking_number][0]['payment_method'], $response['payments'][$this->shipment->tracking_number][0]['payment_id'], 0);

                } else if (in_array($response['payments'][$this->shipment->tracking_number][0]['payment_status'], $ignore)) {

                } else {
                    $activity_id = activity()
                        ->performedOn($this->shipment)
                        ->withProperties(['status' => $response['payments'][$this->shipment->tracking_number][0]['payment_status'] , 'response' => $response ])
                        ->log(ucwords('Trax Payment Status'));
                }
            } else {
                $activity_id = activity()
                    ->performedOn($this->shipment)
                    ->withProperties(['message' => 'shipments payment details not found' , 'response' => $response ])
                    ->log(ucwords('Trax Payment Details Not Found'));
                $this->forceTrackStatus('payment details not received by courier', 'Trax');
            }
        }

        return compact('error', 'message');
    }


    public function blueEx($parameters)
    {
        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier Blue-Ex';

        $data = [   
            'settlement' => json_encode([
                "acno" => $parameters['account_no'],
                "userid" => $parameters['user_id'],
                "password" => $parameters['password'],
                "cnno" => $this->shipment->tracking_number
            ])
        ];

        $response = $this->client->get('https://bigazure.com/api/json_v2/settlement/getSettlement.php', [
            'headers' => [ 'Content-Type' => 'application/x-www-form-urlencoded' ],
            'query' => $data
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);

        Log::info($response);

        if (isset($response['status']) && $response['status'] == 0) {

            $message .= (isset($response['message']) ? ' | '.$response['message'] : ' ');

        } elseif ( isset($response[0]['status']) && $response[0]['status'] == 1 ) {
            
            $payment_date = $response[0]['fps_date'];
            $this->updateShipment($payment_date, 'Blue-Ex');
            $error = 0;
            $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier Blue-Ex';
            $this->saveCODPayment(10, $response[0]['cheque_number'], 'Cheque', $response[0]['fps_code'], $response[0]['total_charges']);
        } else {
            $error = 1;
            $message .= ' | '.json_encode($response);
        }

        return compact('error', 'message');
    }

    public function tcs($parameters)
    {
        $client_id = $parameters['client_id'];
        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier TCS, shipment has unpaid status';
       
        $response = $this->client->get('https://api.tcscourier.com/production/v1/cod/payment-details?consignmentNumber='.$this->shipment->tracking_number, [
            'headers' => [ 
                'x-ibm-client-id' => $client_id
            ]

        ]); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);
        
        if (isset($response) ) {
            if (isset($response['returnStatus']) && isset($response['returnStatus']['code']) && $response['returnStatus']['code'] == "0200" && isset($response['paymentDetails'][0]['STATUS']) && $response['paymentDetails'][0]['STATUS'] == 'PAID') {
                $payment_date = Carbon::now()->toDateTimeString();
                $this->updateShipment($payment_date, 'TCS');
                $error = 0;
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier TCS';
                // $this->saveCODPayment(19, $response[0]['InvoiceNo'], 'IBFT', $response[0]['InvoiceID'], 0);
            } else {
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier TCS';
            }

        }

        

        return compact('error', 'message');
    }

    public function movex($parameters)
    {
        $api_key = $parameters['api_key'];
        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier MoveX, shipment has unpaid status';
       
        $response = $this->client->get('https://tracking.movexpk.com/api/consignment/payment-status?consignment_numbers='.$this->shipment->tracking_number, [
            'headers' => [ 
                'Authorization' => $api_key
            ]

        ]); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $response = json_decode($response, TRUE);
        
        if (isset($response) ) {
            if (isset($response['response']) && isset($response['response']['success']) && count($response['response']['error']) == 0 && isset($response['response']['success'][0]['http-response']) && $response['response']['success'][0]['http-response'] == 200 && $response['response']['success'][0]['payment_status'] == "Done") {
                $payment_date = Carbon::parse($response['response']['success'][0]['deposit_date'])->toDateTimeString();
                $this->updateShipment($payment_date, 'MoveX');
                $error = 0;
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier MoveX';
                // $this->saveCODPayment(19, $response[0]['InvoiceNo'], 'IBFT', $response[0]['InvoiceID'], 0);
            } else {
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier MoveX';
            }

        }

        

        return compact('error', 'message');
    }

    public function stallion($parameters)
    {
        $username = $parameters['username'];
        $password = $parameters['password'];

        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier Stallion, shipment has unpaid status';

        $response1 = $this->client->get(env('STALLION_URL').'oauth/v2/Token', [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'password',
                'username' => $username,
                'password' => $password,
            ],
        ]); 
        
        $responses = $response1->getBody()->getContents(); 
        $responses = json_decode($responses, TRUE);
        $httpCode = $response1->getStatusCode();

        if($httpCode == '200' || $httpCode == '201')
        {
            if(isset($responses['access_token']) && isset($responses['UserID'])){
                $response = $this->client->post(env('STALLION_URL').'Stallion/ShippingChargesAPI/', [
                    'headers' => [
                        'Authorization' => 'Bearer '.$responses['access_token'],
                    ],
                    'form_params' => [
                        "AccountNo" => $responses['UserID'],
                        "CNNumber" => $this->shipment->tracking_number,
                    ],
                ]);
                
                $response = $response->getBody()->getContents(); // Request Response
                $response = json_decode($response, TRUE);
                
                if (isset($response['GetShippingChargesAPI']) ) {
                    if (isset($response['GetShippingChargesAPI'][0]['InvoiceDate'])) {
                        $ts = explode('Date(',$response['GetShippingChargesAPI'][0]['InvoiceDate']);
                        $dt = explode(')',$ts[1]);
                        $payment_date = Carbon::createFromTimestamp(intval((int)$dt[0]/1000))->toDateTimeString();
                        
                        // $payment_date = Carbon::parse($response['response']['success'][0]['deposit_date'])->toDateTimeString();
                        $this->updateShipment($payment_date, 'Stallion');
                        $error = 0;
                        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier Stallion';
                        // $this->saveCODPayment(19, $response[0]['InvoiceNo'], 'IBFT', $response[0]['InvoiceID'], 0);
                    } else {
                        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier Stallion';
                    }
                } else{
                    $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier Stallion | Payment Api Error | params not found';
                }
            } else{
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier Stallion | Token Api Error | token not found';
            }
        } else{
            $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier Stallion | Token Api Error | httpcode not 200';
        }
       
        

        

        return compact('error', 'message');
    }


    public function postex($parameters)
    {
        $token = $parameters['token'];
        $authorization = $parameters['authorization'];

        $error = 2;
        $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not Received Courier PostEx, shipment has unpaid status';

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('POSTEX_API').'/external-app/order-management/v1/payment-status/'.$this->shipment->tracking_number,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
        'token: '.$token,
        'authorization: '.$authorization
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response,true);
        curl_close($curl);


        if(isset($response['statusCode']) && $response['statusCode'] == "200" && isset($response['dist']['settle'])){
            if($response['dist']['settle'] == true && isset($response['dist']['settlementDate'])) {
                $payment_date = Carbon::create($response['dist']['settlementDate'])->toDateTimeString();
                $this->updateShipment($payment_date, 'PostEx');
                $error = 0;
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has been Received Courier PostEx';
            }else{
                $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier PostEx';
            }
        }else{
            $message = 'Shipment #'.$this->shipment->tracking_number.' COD amount has not been Received Courier PostEx';
        }  

        return compact('error', 'message');
    }

}
