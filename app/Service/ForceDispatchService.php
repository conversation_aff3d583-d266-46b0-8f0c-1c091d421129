<?php 

namespace App\Service;

use Exception;
use Illuminate\Support\Facades\Log;

class ForceDispatchService
{

    public static function execute($tracking_number, $seller_id, $status)
    {
        try {

            $authSig = base64_encode(hash_hmac('sha256', 'URxNautIluS', 'yoitsasecretkey', true));
            $url = env('UNITY_URL').'/api/nautilus/force-dispatch-shipment';
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                    "seller_id":'.$seller_id.',
                    "tracking_number":"'.$tracking_number.'",
                    "status":"'.$status.'"
                }',
                CURLOPT_HTTPHEADER => array(
                    'X-Auth: '.$authSig,
                    'Content-Type: application/json',
                ),
            ));

            $response = curl_exec($curl);
            $response = json_decode($response, TRUE);
            curl_close($curl);

            Log::info('Force Dispatch Unity ('.$tracking_number.') Response | '.json_encode($response));

        } catch(Exception $e) {
            
            Log::info('Force Dispatch Unity ('.$tracking_number.') Error | '.json_encode($e->getMessage()));
            Log::info('Force Dispatch Unity ('.$tracking_number.') Error | '.json_encode($e->getTraceAsString()));
        }
    }

}