<?php 

namespace App\Service;

use App\Events\KhaadiShipmentTrackingEvent;
use App\Events\ShipmentTrackingEvent;
use App\Helpers\StorefrontComment;
use App\Models\CODPayment;
use App\Models\CODPaymentShipment;
use App\Models\Order;
use App\Models\OrderComments;
use App\Models\SellerPaymentMethod;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentCharges;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Exception;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class ShopifyService
{

    static public function markPaid($order)
        {
            $store = Setting::where('seller_id', $order->seller_id)->where('key','WC')->first();
            $accessToken = Setting::where('seller_id', $order->seller_id)->where('key','ShopifyToken')->first();

            $payment_method = SellerPaymentMethod::find($order->seller_payment_method_id);

            $transaction_id_result = self::getTransactionId($order,$store, $accessToken);

            if($transaction_id_result['error'] == 0){

                $client = new Client();
                $headers = ['X-Shopify-Access-Token' => $accessToken->value];

                $data = [
                    "transaction" => [
                        "kind" => 'capture', 
                        "gateway" => ($payment_method) ? $payment_method->machine_name : '', 
                        'amount' =>  number_format($order->grand_total,2,'.',''), 
                        'parent_id' => $transaction_id_result['result'], 
                        'status' => 'success',
                        'currency' => $order->currency
                    ]
                ];
        
                $fulfill = $client->request('POST', 'https://'.$store->value.'/admin/api/2022-04/orders/'.$order->marketplace_id.'/transactions.json',[
                    'headers' => $headers,
                    'json' => $data
                ]);
                $http_code = $fulfill->getStatusCode();
                $fulfillment_id = json_decode($fulfill->getBody());
                // $fulfillment_id = $fulfillment_id->fulfillment->id;
                Log::info('Post response | ' . json_encode($fulfillment_id));
                if($http_code == 201 || $http_code == 200){
                    return array('error' => 0,'message' => 'Shopify | Order Marked Paid Successfully');
                } else{
                    return array('error' => 1,'message' => 'Shopify | Something went wrong');
                }

            } else{
                return array('error' => 0,'message' => 'Shopify | Transaction ID not found');
            }
        }

        static public function getTransactionId($order,$store,$accessToken)
        {
            // $store = Setting::where('seller_id', $order->seller_id)->where('key','WC')->first();
            // $accessToken = Setting::where('seller_id', $order->seller_id)->where('key','ShopifyToken')->first();
            // Log::info('3st');

            $client = new Client();
            $headers = ['X-Shopify-Access-Token' => $accessToken->value];

          
    
            $fulfill = $client->request('GET', 'https://'.$store->value.'/admin/api/2022-04/orders/'.$order->marketplace_id.'/transactions.json',[
                'headers' => $headers,
            ]);
            // Log::info('4st');
            $http_code = $fulfill->getStatusCode();
            $result = json_decode($fulfill->getBody()->getContents(),true);


            // $fulfillment_id = $fulfillment_id->fulfillment->id;
            // Log::info('5st');
            Log::info(json_encode($result));
            Log::info('Get response | ' . json_encode($result));
            if(isset($result['transactions'][0]['id'])){
                return array('error' => 0, 'result' => $result['transactions'][0]['id']);
            } else{
                return array('error' => 1, 'result' => null);
            }
        }

}