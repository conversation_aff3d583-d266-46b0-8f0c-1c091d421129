<?php 

namespace App\Helpers;

use App\Models\Courier\Courier;
use App\Models\Courier\SellerCourierQuiqup;
use App\Models\Courier\SellerCourierRider;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\Order;
use App\Models\OrderComments;
use App\Models\OrderItem;
use App\Models\Shipment;
use App\Models\Setting;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TrackingRequest
{
    private $client;

    public function __construct()
    {
		$this->client = new Client();
    }

    public function stringMatch($string, $array)
    {
        foreach ($array as $value) {
            if (stripos($string,$value) !== false) {
                return true;
            }
        }
        return false;
    }

    public function track($shipment)
    {
        if ($shipment->courier_id == 4) {
            return $this->mnp($shipment);
        } elseif ($shipment->courier_id == 8) {
            return $this->trax($shipment);
        } elseif ($shipment->courier_id == 11) {
            return $this->rider($shipment);
        } else {
            return ['error' => 1, 'message' => 'Courier Not Found'];
        }
    }
    
    public function mnp($shipment)
    {
        $error = 0;
        $message = 'success';
        $shipment_status = Null;
        $order_item_status = Null;
        $shipment_status_date = Null;
        $courier = $shipment->courier_id;

        $booked_statuses = ['Booking'];
        $dispatched_statuses = [ 'Arrived at OPS'];
        $pending_delivery_statuses = ['NCI','Short Piece/Missing','Saturday Closed','DEBAG','DEMANIFEST','LOADED','UNLOAD','BAGGED','Non Service Area','ROUTED','Friday Closed','Unable to Locate','No Such Consignee','Bad Address','Closed on Arrival','Refused Delivery','Not Home','Loading', 'Unloading','On Delivery','Consignee Shifted','MANIFESTED'];
        $returned_statuses = ['Returned','RETURNED','Return to Shipper'];
        $pending_return_statuses = ['Being Return','Return To Sender','Return to Origin','Ready to Return'];
        $delivered_statuses = ['Delivered'];
        $cancelled_statuses = ['Cancelled','Canceled'];

        try {
            $response = $this->client->get('http://mnpcourier.com/mycodapi/api/Tracking/Tracking?consignment='.$shipment->tracking_number);
            $response = $response->getBody()->getContents();
            $response = json_decode($response, TRUE);

            if (isset($response[0]['isSuccess']) && $response[0]['isSuccess'] == 'true') {

                if (empty($response[0]['tracking_Details'][0]['Details'])) {
                    
                    $status = $response[0]['tracking_Details'][0]['CNStatus'];
                    $shipment_status_date = Carbon::now()->toDateTimeString();

                    // if ($status) {
                    //     $shipment->shipment_courier_history($status, $shipment_status_date, 'Details not found against this shipment, shipment overall status is used');
                    // }

                    if ($this->stringMatch($status, $cancelled_statuses)) {
                        $shipment_status = config('enum.shipment_status')['CANCELLED'];
                        $order_item_status = config('enum.item_status')['PENDING'];
                    }
                    else if ($this->stringMatch($status, $booked_statuses)) {
                        // Ignore
                    }
                    else if ($this->stringMatch($status, $dispatched_statuses)) {
                        $shipment_status = config('enum.shipment_status')['DISPATCHED'];
                    }
                    else if ($this->stringMatch($status, $pending_delivery_statuses)) {
                        $shipment_status = config('enum.shipment_status')['PENDING_DELIVERY'];
                    }
                    else if ($this->stringMatch($status, $returned_statuses)) {
                        $shipment_status = config('enum.shipment_status')['RETURNED'];
                        $order_item_status = config('enum.item_status')['RETURNED'];
                    }
                    else if($this->stringMatch($status, $pending_return_statuses)){
                        $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
                    }
                    else if ($this->stringMatch($status, $delivered_statuses)) {
                        $order_item_status = config('enum.item_status')['COMPLETED'];
                        $shipment_status = config('enum.shipment_status')['DELIVERED'];
                    }
                    // else if (!$status || $status == '') {
                    //     if (($shipment->created_at->diffInDays(Carbon::now())) > 120) {

                    //         $shipment_status_date = Carbon::now()->toDateTimeString();
                    //         $shipment->shipment_courier_history(config('enum.shipment_status')['CANCELLED'], $shipment_status_date, 'Shipment is Cancelled due to empty CN Status in response and it is older than 120 days');
        
                    //         $shipment->update(['status' => config('enum.shipment_status')['CANCELLED']]);
                    //         $shipment->shipment_history(config('enum.shipment_status')['CANCELLED'],$shipment_status_date);
        
                    //         $order_item_status = config('enum.item_status')['CANCELLED'];
        
                    //         OrderComments::add($shipment->order_id, 'Shipment Tracking Process', 'Shipment #'.$shipment->tracking_number.' is Cancelled due to empty CN Status in response and it is older than 120 days. | <b>'.json_encode($response).'</b>', 'Failed', 1);
                    //         Log::info('MnP Tracking | Shipment Cancelled | '.$shipment->tracking_number.' | '.json_encode($response));
                    //     }
                    // }
                    // else {
                    //     if (!in_array('MnP | '.$status,$notMappedStatuses)) {
                    //         array_push($notMappedStatuses,'MnP | '.$status);
                    //     }
                    // }


                } else {
                    foreach (array_reverse($response[0]['tracking_Details'][0]['Details']) as $detail) {
                    
                        if ($detail) {
                            $status = $detail['Status'];
                            $shipment_status_date = Carbon::parse($detail['DateTime']);
                            
                            // if ($shipment_status_date->gt($shipment->last_run)) {

                                $shipment_status_date = $shipment_status_date->toDateTimeString();
                                // $shipment->shipment_courier_history($status,$shipment_status_date,( isset($detail['Detail']) ? $detail['Detail'] : '-') );
    
                                if (in_array($status, $cancelled_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['CANCELLED'];
                                    $order_item_status = config('enum.item_status')['PENDING'];
                                }
                                else if (in_array($status, $booked_statuses)) {
                                    // Ignore
                                }
                                else if (in_array($status, $dispatched_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['DISPATCHED'];
                                }
                                else if (in_array($status, $pending_delivery_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['PENDING_DELIVERY'];
                                }
                                else if (in_array($status, $returned_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['RETURNED'];
                                    $order_item_status = config('enum.item_status')['RETURNED'];
                                }
                                else if(in_array($status, $pending_return_statuses)){
                                    $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
                                }
                                else if (in_array($status, $delivered_statuses)) {
                                    $order_item_status = config('enum.item_status')['COMPLETED'];
                                    $shipment_status = config('enum.shipment_status')['DELIVERED'];
                                }
                                // else {
                                //     if (!in_array('MnP | '.$status,$notMappedStatuses)) {
                                //         array_push($notMappedStatuses,'MnP | '.$status);
                                //     }
                                // }
                            // }
                        }
                    }
                }
            }
        } catch(Exception $e) {
            $message = $e->getMessage();
            $error = 1;

            $activity_id = activity()
            ->performedOn($shipment)
            ->withProperties(['response' => $message, 'dump' => $e->getTraceAsString()])
            ->log('MnP Tracking');
        }

        if (!$shipment_status) {
            $error = 1;
        }

        return compact('error','order_item_status','shipment_status','shipment_status_date','message','courier');
    }
    
    public function trax($shipment)
    {
        $error = 0;
        $message = 'success';
        $shipment_status = Null;
        $order_item_status = Null;
        $shipment_status_date = Null;
        $courier = $shipment->courier_id;

        $booked_statuses = ['Shipment - Booked'];
        $dispatched_statuses = ['Shipment - Arrival Service Center','Shipment - Arrived at Origin'];
        $pending_delivery_statuses = ['Shipment - In Transit', 'Shipment - Arrived at Destination', 'Shipment - Out for Delivery', 'Shipment - Rider Exchange', 'Shipment - Not Attempted', 'Shipment - Delivery Unsuccessful', 'Shipment - On Hold', 'Shipment - Non-Service Area', 'Shipment - Misrouted', 'Shipment - Re-Attempt Requested', 'Shipment - Re-Attempt', 'Shipment - On Hold for Self Collection', 'Shipment - Misroute Forwarded', 'Intercept Approved', 'Replacement-Not Collected', 'Replacement - In Transit', 'Replacement - Arrived at Origin', 'Replacement - Dispatched', 'Replacement - Delivery Unsuccessful', 'Replacement - Collected', 'Replacement - Rider Exchange'];
        $returned_statuses = ['Return - Delivered to Shipper'];
        $pending_return_statuses = ['Intercept Requested','Return - Confirmation Pending','Return - Confirm','Return - In Transit','Return - Arrived at Origin','Return - Dispatched','Return - Delivery Unsuccessful','Return - Rider Exchange','Return - Not Attempted','Return - On Hold','Return - Not Attempted','Return - On Hold'];
        $delivered_statuses = ['Shipment - Delivered', 'Replacement - Delivered to Shipper'];
        $cancelled_statuses = ['Shipment - Cancelled'];
        $lost_statuses = ['Shipment - Month Closing', 'Shipment - Lost', 'Shipment - Case Closed'];

        try {
            $TraxNew = SellerCourierTraxNew::where('seller_id', $shipment->seller_id)->where('courier_id', 8)->first();

            $response = $this->client->get('https://sonic.pk/api/shipment/track?tracking_number='.$shipment->tracking_number.'&type=1', [
                'headers' => [ 'Authorization' => $TraxNew->authorization_key ]
            ]);
            $response = $response->getBody()->getContents();
            $response = json_decode($response, TRUE);

            if ($response['status'] == 0) {

                foreach (array_reverse($response['details']['tracking_history']) as $key => $value) {
                
                    $status = $value['status'];
                    $shipment_status_date = Carbon::createFromFormat('d/m/Y g:i A',$value['date_time']);

                    // if (Carbon::parse($shipment_status_date)->gt($shipment->last_run)) {

                        $shipment_status_date = $shipment_status_date->toDateTimeString();
                        // $shipment->shipment_courier_history($status, $shipment_status_date, $value['status_reason'] );

                        if (in_array($status, $cancelled_statuses)) {
                            $shipment_status = config('enum.shipment_status')['CANCELLED'];
                            $order_item_status = config('enum.item_status')['PENDING'];
                        }
                        else if (in_array($status, $booked_statuses)) {
                            // Ignore
                        }
                        else if (in_array($status, $dispatched_statuses)) {
                            $shipment_status = config('enum.shipment_status')['DISPATCHED'];
                        }
                        else if (in_array($status, $pending_delivery_statuses)) {
                            $shipment_status = config('enum.shipment_status')['PENDING_DELIVERY'];
                        }
                        else if (in_array($status, $returned_statuses)) {
                            $shipment_status = config('enum.shipment_status')['RETURNED'];
                            $order_item_status = config('enum.item_status')['RETURNED'];
                        }
                        else if (in_array($status, $pending_return_statuses)){
                            $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
                        }
                        else if (in_array($status, $delivered_statuses)) {
                            $order_item_status = config('enum.item_status')['COMPLETED'];
                            $shipment_status = config('enum.shipment_status')['DELIVERED'];
                        }
                        else if (in_array($status, $lost_statuses)) {
                            $order_item_status = config('enum.item_status')['PENDING'];
                            $shipment_status = config('enum.shipment_status')['LOST'];
                        }
                        // else {
                        //     if (!in_array('Trax New | '.$status,$notMappedStatuses)) {
                        //         array_push($notMappedStatuses,'Trax New | '.$status);
                        //     }
                        // }
                    // }
                }
            } else if($response['status'] == 1 && ($shipment->created_at->diffInDays(Carbon::now())) > 120) {
                // $shipment_status_date = Carbon::now()->toDateTimeString();
                // $shipment->shipment_courier_history(config('enum.shipment_status')['CANCELLED'], $shipment_status_date, 'Shipment is Cancelled because tracking details not found in response and it is older than 120 days');

                // $shipment->update(['status' => config('enum.shipment_status')['CANCELLED']]);
                // $shipment->shipment_history(config('enum.shipment_status')['CANCELLED'],$shipment_status_date);

                // $order_item_status = config('enum.item_status')['CANCELLED'];

                // OrderComments::add($shipment->order_id, 'Shipment Tracking Process', 'Shipment #'.$shipment->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days. | <b>'.json_encode($response).'</b>', 'Failed', 1);
                // Log::info('Trax Tracking | Shipment Cancelled | '.$shipment->tracking_number.' | '.json_encode($response));
            }
        } catch(Exception $e) {
            $message = $e->getMessage();
            $error = 1;

            $activity_id = activity()
            ->performedOn($shipment)
            ->withProperties(['response' => $message, 'dump' => $e->getTraceAsString()])
            ->log('Trax New Tracking');
        }

        if (!$shipment_status) {
            $error = 1;
        }

        return compact('error','order_item_status','shipment_status','shipment_status_date','message','courier');
    }
    
    public function rider($shipment)
    {
        $error = 0;
        $message = 'success';
        $shipment_status = Null;
        $order_item_status = Null;
        $shipment_status_date = Null;
        $courier = $shipment->courier_id;

        $booked_statuses = ['Rider on its way','Click to Call a Rider','Rider Rejected'];
        $dispatched_statuses = ['Collected'];
        $pending_delivery_statuses = ['Ready for Delivery','Rider is out for Delivery','Delivery in progress','Delivery Attempt Failed','Awaiting Dispatch','In Transit','Awaiting Transit'];
        $returned_statuses = ['Returned'];
        $pending_return_statuses = ['Return in Progress','Return Attempt Failed','QC Rejected - Hub','Awaiting Return','Return In Transit','Return To Origin'];
        $delivered_statuses = ['Delivered'];
        $cancelled_statuses = ['Cancelled'];
        $lost_statuses = ['Lost'];

        try {

            if ($shipment->type == Null) {
                $customer_number = $shipment->order->customer_number;
            } else {
                $customer_number = $shipment->customer_number;
            }
            $rider = SellerCourierRider::where('seller_id', $shipment->seller_id)->where('courier_id', 11)->first();

            $response = $this->client->get('http://api.withrider.com/rider/v1/GetStatusHistory?cn='.$shipment->tracking_number.'&phone='.$customer_number.'&loginId='.$rider->login_id.'&apikey='.$rider->api_key);
            $response = $response->getBody()->getContents();

            if ($response) {
                $response = json_decode($response, TRUE);

                foreach ($response as $key => $value) {
                
                    if(isset($value['order_status'])) {

                        $status = $value['order_status'];
                        $shipment_status_date = Carbon::parse($value['updated_at']);

                        // if (Carbon::parse($shipment_status_date)->gt($shipment->last_run)) {

                            $shipment_status_date = $shipment_status_date->toDateTimeString();
                            // $shipment->shipment_courier_history($status, $shipment_status_date, $value['reason'] );
                        
                            if ($this->stringMatch($status, $cancelled_statuses)) {
                                $shipment_status = config('enum.shipment_status')['CANCELLED'];
                                $order_item_status = config('enum.item_status')['PENDING'];
                            }
                            else if ($this->stringMatch($status, $booked_statuses)) {
                                // Ignore
                            }
                            else if ($this->stringMatch($status, $dispatched_statuses)) {
                                $shipment_status = config('enum.shipment_status')['DISPATCHED'];
                            }
                            else if ($this->stringMatch($status, $pending_delivery_statuses)) {
                                $shipment_status = config('enum.shipment_status')['PENDING_DELIVERY'];
                            }
                            else if ($this->stringMatch($status, $returned_statuses)) {
                                $shipment_status = config('enum.shipment_status')['RETURNED'];
                                $order_item_status = config('enum.item_status')['RETURNED'];
                            }
                            else if($this->stringMatch($status, $pending_return_statuses)){
                                $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
                            }
                            else if ($this->stringMatch($status, $delivered_statuses)) {
                                $order_item_status = config('enum.item_status')['COMPLETED'];
                                $shipment_status = config('enum.shipment_status')['DELIVERED'];
                            }
                            else if ($this->stringMatch($status, $lost_statuses)) {
                                $shipment->update(['status' => config('enum.shipment_status')['LOST']]);
                                $shipment->shipment_history(config('enum.shipment_status')['LOST'],$shipment_status_date);

                                foreach ($shipment->items as $value) {
                                    $value->order_item->status = config('enum.item_status')['PENDING'];
                                    $value->order_item->save();
                                }
                                $shipment->order->update_status_according_to_items();
                                $shipment_status = Null;
                                OrderComments::add($shipment->order_id, 'Shipment Tracking Process', 'Shipment #'.$shipment->tracking_number.' is now has a <b>'.$status.'</b> status', 'Success', '1' );
                            }
                            // else {
                            //     if (!in_array('Rider | '.$status,$notMappedStatuses)) {
                            //         array_push($notMappedStatuses,'Rider | '.$status);
                            //     }
                            // }
                        // }
                    }
                }
            }
        } catch(RequestException $e) {
            $error = 1;

            $error_response = json_decode(((string) $e->getResponse()->getBody()), true);
        
            if ( isset($error_response['message']) ) {
                $message = $error_response['message'];
            } elseif ( isset($error_response['errors']) ) {
                $message = json_encode($error_response['errors']);
            } else {
                $message = $e->getMessage();
            }

            // if ($message == "Consignment id not found / Tracking history not available" && ($shipment->created_at->diffInDays(Carbon::now())) > 120) {
            //     $shipment->update(['status' => config('enum.shipment_status')['CANCELLED']]);
            //     $shipment->shipment_history(config('enum.shipment_status')['CANCELLED'],Carbon::now()->toDateTimeString());

            //     $order_item_status = config('enum.item_status')['CANCELLED'];

            //     OrderComments::add($shipment->order_id, 'Shipment Tracking Process', 'Shipment #'.$shipment->tracking_number.' is Cancelled because its record not exits at TPL Rider. | <b>'.$message.'</b> | and its older than 120 days', 'Failed', 1);
            //     Log::info('Rider Tracking | Shipment Cancelled | '.$shipment->tracking_number.' | '.$message);  
            // }
            
            $activity_id = activity()
            ->performedOn($shipment)
            ->withProperties(['response' => $message , 'dump' => $e->getTraceAsString()])
            ->log('Rider Tracking');
        }

        if (!$shipment_status) {
            $error = 1;
        }

        return compact('error','order_item_status','shipment_status','shipment_status_date','message','courier');
    }

    public static function bykea($data, $message = null)
    {
        try {

            $data = (object) $data;

            if (stripos($data->event,'booking') !== false) {

                if (!isset($data->data['trip_id'])) {
                    return response()->json(['message' => 'Trip Id not found'], 400);
                }
                
                $shipment = Shipment::whereTrackingNumber($data->data['trip_id']);
                if ($shipment->exists()) {
                    $shipment = $shipment->first();

                    $message = 'Trip ID : '.$data->data['trip_id'].' | ';
                    $shipment_status_date = Carbon::createFromTimestamp(($data->event_time)/1000)->toDateTimeString(); 

                    if ($data->event == 'booking.created') {  // Booking created
                        $message .= 'Booking created on bykea portal';

                    } elseif ($data->event == 'booking.updated.trackinglink') {  // tracking started
                        $message .= 'Tracking link generated. <a target="blank" href="'.$data->data['tracking_link'].'">'.$data->data['tracking_link'].'</a>';

                    } elseif ($data->event == 'booking.accepted') {  // Booking accepted
                        $message .= 'Booking is accepted by a rider. | '.
                                    $data->data['partner']['name'].' | '.
                                    $data->data['partner']['plate_no'].' | '.
                                    $data->data['partner']['mobile'];

                    } elseif ($data->event == 'booking.arrived') {  // Booking arrived
                        $message .= 'Rider has arrived at your pickup location.';
                        Shipment::on('mysql3')->where('id',$shipment->id)->update(['status' => config('enum.shipment_status')['DISPATCHED']]);
                        $shipment->shipment_history(config('enum.shipment_status')['DISPATCHED'], ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                    
                    } elseif ($data->event == 'booking.started') {  // Booking started
                        $message .= 'Package has dispatch , Rider on its way to delivery.';
                        Shipment::on('mysql3')->where('id',$shipment->id)->update(['status' => config('enum.shipment_status')['PENDING_DELIVERY']]);
                        $shipment->shipment_history(config('enum.shipment_status')['PENDING_DELIVERY'], ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                    
                    } elseif ($data->event == 'booking.finished') { // Booking finished
                        $message .= 'Package has been delivered';
                        Shipment::on('mysql3')->where('id',$shipment->id)->update(['delivered_at' => $shipment_status_date, 'status' => config('enum.shipment_status')['DELIVERED']]);
                        $shipment->shipment_history(config('enum.shipment_status')['DELIVERED'], ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                    
                        OrderItem::on('mysql3')->whereIn('id',$shipment->items->pluck('order_items_id'))->update(['status' => config('enum.item_status')['COMPLETED']]);
                        Order::re_evaluate_status($shipment->order_id);

                    } else { // Event not found
                        Mail::raw('Invalid Event | '.$data->event, function ($m)  {
                            $m->to('<EMAIL>')->subject('Bykea Tracking');
                        });
                        Log::info('Invalid Event | '.$data->event);
                    }
                    
                    $shipment->shipment_courier_history($data->event, $shipment_status_date, $message);

                } else {
                    return response()->json(['message' => 'Trip Id not exists in our list'], 400);
                }


            } elseif (stripos($data->event,'batch') !== false) {
                
                if (!isset($data->data['batch_id'])) {
                    return response()->json(['message' => 'Batch Id not found'], 400);
                }

                $batch = DB::connection('mysql2')->table('extra_columns')->where('keyable_type','App\Models\Shipment')->where('value',$data->data['batch_id']);
                if ($batch->exists()) {
                    $batch = $batch->first();

                    $shipment = Shipment::find($batch->keyable_id);
                    $message = 'Batch ID : '.$data->data['batch_id'].' | ';
                    $shipment_status_date = Carbon::createFromTimestamp(($data->event_time)/1000)->toDateTimeString(); 

                    if ($data->event == 'batch.created') {  // Booking created
                        $message .= 'Batch created on bykea portal';

                    } elseif ($data->event == 'batch.cancelled.partner') {  // batch cancel
                        $message .= 'Previous rider has cancel the delivery request , now assigning another rider . ';

                    } elseif ($data->event == 'batch.accepted') {  // batch accepted
                        $message .= 'Batch is accepted by a rider.';

                    } elseif ($data->event == 'batch.arrived') {  // Batch arrived
                        $message .= 'Rider has arrived at your pickup location.';
                    
                    } elseif ($data->event == 'batch.started') {  // Batch started
                        $message .= 'Package has dispatch , Rider on its way to delivery.';
                    
                    } elseif ($data->event == 'batch.completed') { // Batch finished
                        $message .= 'Batch has been completed';
                    
                    } elseif ($data->event == 'batch.cancelled.customer') { // Batch finished
                        $message .= 'Batch has been cancelled by shipper itself';
                    
                    } else { // Event not found
                        Mail::raw('Invalid Event | '.$data->event, function ($m)  {
                            $m->to('<EMAIL>')->subject('Bykea Tracking');
                        });
                        Log::info('Invalid Event | '.$data->event);
                    }
                    
                    $shipment->shipment_courier_history($data->event, $shipment_status_date, $message);

                } else {
                    return response()->json(['message' => 'Batch Id not exists in our list'], 400);
                }
            } else {
                Mail::raw('Invalid Event | '.$data->event, function ($m)  {
                    $m->to('<EMAIL>')->subject('Bykea Tracking');
                });
                Log::info('Invalid Event | '.$data->event);
                return response()->json(['message' => 'Invalid Event'], 400);
            }

        } catch (Exception $e) {
            Mail::raw($e->getMessage(), function ($m)  {
                $m->to('<EMAIL>')->subject('Bykea Tracking');
            });
            Log::info($e->getTraceAsString());
            return response()->json(['message' => 'Internal error'], 500);
        }

        return response()->json(['message' => 'Success'], 200);
    }

    public static function quiqup($data, $message = null)
    {
        try {

            // $data = (object) $data;
           
            $body = $data->all();
            $content = $data->getContent();

            Log::info('Quiqup | Tracking Data | '.$data);

            // $quiqup_api_key = config('enum.quiqup')['APIKEY'];
            $quiqup_token = env('QUIQUPTOKEN');

            Log::info(hash_hmac('sha1', $content, $quiqup_token));

            // if($quiqup_api_key == $data->header('X-API-KEY') || $quiqup_api_key == $data->header('X-APIKEY')){
                $sig = explode('=',$data->header('X-Signature'));
                $auth_sig = hash_hmac('sha1', $content, $quiqup_token);

                if($auth_sig == $sig[1]){

                    // log::info(Carbon::parse($body['payload']['state_updated_at']));
                    $terminal_status = [
                        config('enum.shipment_status')['DELIVERED'],
                        config('enum.shipment_status')['CANCELLED'],
                        config('enum.shipment_status')['RETURNED'],
                        config('enum.shipment_status')['LOST']
                    ];

                    $notMappedStatuses = [];
                    $shipment_status = null;
                    $order_item_status = null;

                    
                    //Log::info('Quiqup | body | '.$body);
                    $shipment = Shipment::whereTrackingNumber($body['payload']['id'])
                                ->whereCourierId(23)
                                ->whereNotIn('status',$terminal_status)
                                ->first();

                    if($shipment){

                        $proper_status_arr = [
                            'ready_for_collection' => 'Ready for Collection',
                            'collected' => 'Collected',
                            'received_at_depot' => 'Received at Depot',
                            'out_for_delivery' => 'Out for Delivery',
                            'at_depot' => 'At Depot',
                            'on_hold' => 'On Hold',
                            'in_transit' => 'InTransit',
                            'scheduled' => 'Scheduled',
                            'delivery_failed' => 'Delivery Failed',
                            'returned_to_origin' => 'Returned to Origin',
                            'return_to_origin' => 'Return to Origin',
                            'delivery_complete' => 'Delivery Complete',
                            'cancelled' => 'Cancelled',
                            'out_for_collection' => 'Out for Collection',
                            'out_for_return' => 'Out for Return'
                        ];
                        
                        $booked_statuses = ['ready_for_collection','out_for_collection'];
                        $dispatched_statuses = ['collected'];
                        $pending_delivery_statuses = ['received_at_depot','out_for_delivery','in_transit','at_depot','on_hold','scheduled','delivery_failed'];
                        $returned_statuses = ['returned_to_origin'];
                        $pending_return_statuses = ['return_to_origin','out_for_return'];
                        $delivered_statuses = ['delivery_complete'];
                        $cancelled_statuses = ['cancelled'];

                        

                        if (isset($data['payload']) && count($data['payload']) > 0) {

                            
                            $status = $body['payload']['state'];
                            // $status = $proper_status_arr[$status]
                            // $shipment_status_date = $body['payload']['state_updated_at'];
                            $shipment_status_date = Carbon::parse($body['payload']['state_updated_at'],'GMT+4')->setTimezone(config('app.timezone'));
                            
                            if ($shipment_status_date->gt($shipment->last_run)) {
                            
                                $shipment_status_date = $shipment_status_date->toDateTimeString();
                                $shipment->shipment_courier_history($proper_status_arr[$status], $shipment_status_date, ($body['payload']['delivery_failure_reason'] != null) ? $body['payload']['delivery_failure_reason'] : '-' );

                                if (in_array($status, $cancelled_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['CANCELLED'];
                                    $order_item_status = config('enum.item_status')['PENDING'];
                                }
                                else if (in_array($status, $booked_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['BOOKED'];
                                }
                                else if (in_array($status, $dispatched_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['DISPATCHED'];
                                }
                                else if (in_array($status, $pending_delivery_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['PENDING_DELIVERY'];
                                }
                                else if (in_array($status, $pending_return_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
                                }
                                else if (in_array($status, $returned_statuses)) {
                                    $shipment_status = config('enum.shipment_status')['RETURNED'];
                                    $order_item_status = config('enum.item_status')['RETURNED'];
                                }
                                else if (in_array($status, $delivered_statuses)) {
                                    Shipment::on('mysql3')->where('id',$shipment->id)->update(['delivered_at' => $shipment_status_date, 'status' => config('enum.shipment_status')['DELIVERED']]);
                                    $shipment->shipment_history(config('enum.shipment_status')['DELIVERED'], $shipment_status_date);
                                    
                                    $order_item_status = config('enum.item_status')['COMPLETED'];
                                    $shipment_status = Null;
                                }
                                else {
                                    if (!in_array('Quiqup | '.$status,$notMappedStatuses)) {
                                        array_push($notMappedStatuses,'Quiqup | '.$status);
                                    }
                                }

                            

                                // /////// Updating SHIPMENT status if needed
                                if ($shipment_status && $shipment_status != $shipment->status) {
                                    Shipment::on('mysql3')->where('id',$shipment->id)->update(['status' => $shipment_status]);
                                    $shipment->shipment_history($shipment_status, ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                                }
                                
                                /////// Updating ORDER ITEM status if needed
                                if ($order_item_status) {

                                    OrderItem::on('mysql3')->whereIn('id',$shipment->items->pluck('order_items_id'))->update(['status' => $order_item_status]);
                                    
                                    if($order_item_status == config('enum.item_status')['PENDING']) {
                                        Order::re_evaluate_status($shipment->order_id);
                                    }
                                }

                                /////// Updating ORDER status if needed
                                if ($shipment->type == Null && $order_item_status && $order_item_status != config('enum.item_status')['PENDING']) {
                                    Order::re_evaluate_status($shipment->order_id);
                                }

                                if (count($notMappedStatuses) > 0) {
                                    foreach ($notMappedStatuses as $key => $value) {
                                        Log::info('Not Mapped Status | '.$value);
                                    }
                                }
                            }
                        }
                        else{
                            Log::info('Quiqup | Tracking Error | Payload either not found or might be empty');
                            return response()->json(['message' => 'Payload parameter either not found or might be empty'], 404);
                        }
                    }
                    else{
                        // Mail::raw('Quiqup | Tracking Error | Shipment not found', function ($m)  {
                        //     $m->to('<EMAIL>')->subject('Quiqup Tracking');
                        // });
                        Log::info('Quiqup | Tracking Error | Shipment not found');
                        return response()->json(['message' => 'Shipment not found'], 404);
                    }
                }
                else{
                    // Mail::raw('Quiqup | Tracking Error | Authentication Failed', function ($m)  {
                    //     $m->to('<EMAIL>')->subject('Quiqup Tracking');
                    // });
                    Log::info('Quiqup | Tracking Error | Authentication Failed');
                    return response()->json(['message' => 'Authentication Failed'], 404);
                }
            // }
            // else{
            //     Log::info('Quiqup | Tracking Error | Authentication Failed');
            //     return response()->json(['message' => 'Authentication Failed'], 404);
            // }
        } catch (Exception $e) {
            Mail::raw($e->getMessage(), function ($m)  {
                $m->to('<EMAIL>')->subject('Quiqup Tracking');
            });
            Log::info('Quiqup | Tracking Error | '.$e->getTraceAsString());
            return response()->json(['message' => 'Internal error'], 500);
        }

        return response()->json(['message' => 'Success'], 200);
    }

    public static function pandago($data, $message = null)
    {
        try {

            // $data = (object) $data;
           
            $body = $data->all();
            $content = $data->getContent();

            Log::info('Pandago | Tracking Data | '.$data);

            // $quiqup_api_key = config('enum.quiqup')['APIKEY'];

            // $quiqup_token = env('QUIQUPTOKEN');//

            // Log::info(hash_hmac('sha1', $content, $quiqup_token));//

            
            // $sig = explode('=',$data->header('X-Signature'));//
            // $auth_sig = hash_hmac('sha1', $content, $quiqup_token);//
            // if($auth_sig == $sig[1]){//
            if(1==1){

                // log::info(Carbon::parse($body['payload']['state_updated_at']));
                $terminal_status = [
                    config('enum.shipment_status')['DELIVERED'],
                    config('enum.shipment_status')['CANCELLED'],
                    config('enum.shipment_status')['RETURNED'],
                    config('enum.shipment_status')['LOST']
                ];

                $notMappedStatuses = [];
                $shipment_status = null;
                $order_item_status = null;

                

                $shipment = Shipment::whereTrackingNumber($body['order_id'])
                            ->whereCourierId(24)
                            ->whereNotIn('status',$terminal_status)
                            ->first();

                if($shipment){

                    $proper_status_arr = [
                        'NEW' => 'New',
                        'RECEIVED' => 'Received',
                        'WAITING_FOR_TRANSPORT' => 'Waiting for Transport',
                        'ASSIGNED_TO_TRANSPORT' => 'Assigned to Transport',
                        'NEAR_VENDOR' => 'Near Vendor',
                        'PICKED_UP' => 'Picked Up',
                        'COURIER_LEFT_VENDOR' => 'Courier Left Vendor',
                        'NEAR_CUSTOMER' => 'Near Customer',
                        'DELIVERED' => 'Delivered',
                        'CANCELLED' => 'Cancelled',
                    ];
                    
                    $booked_statuses = ['NEW','RECEIVED','WAITING_FOR_TRANSPORT','ASSIGNED_TO_TRANSPORT','NEAR_VENDOR'];
                    $dispatched_statuses = ['PICKED_UP'];
                    $pending_delivery_statuses = ['COURIER_LEFT_VENDOR','NEAR_CUSTOMER'];
                    $returned_statuses = [''];
                    $pending_return_statuses = [''];
                    $delivered_statuses = ['DELIVERED'];
                    $cancelled_statuses = ['CANCELLED'];

                    

                    if (isset($data['status'])) {

                        
                        $status = $data['status'];
                        // $status = $proper_status_arr[$status]
                        // $shipment_status_date = $body['payload']['state_updated_at'];
                        $shipment_status_date = Carbon::parse($body['updated_at']);
                        
                        if ($shipment_status_date->gt($shipment->last_run)) {
                        
                            $shipment_status_date = $shipment_status_date->toDateTimeString();
                            $shipment->shipment_courier_history($proper_status_arr[$status], $shipment_status_date );

                            if (in_array($status, $cancelled_statuses)) {
                                $order_item_status = config('enum.item_status')['PENDING'];
                                if(Setting::where('seller_id', $shipment->seller_id)->where('key',config('enum.settings')['MARKASRETURN'])->where('value',1)->exists()){
                                    $shipment_status = config('enum.shipment_status')['RETURNED'];
                                    self::asignTagToOrder($shipment->order_id);
                                    Log::info('Pandago | Setting Enabled | Sending Tag assignment Request');
                                }else{
                                    $shipment_status = config('enum.shipment_status')['CANCELLED'];
                                }
                            }
                            else if (in_array($status, $booked_statuses)) {
                                $shipment_status = config('enum.shipment_status')['BOOKED'];
                            }
                            else if (in_array($status, $dispatched_statuses)) {
                                $shipment_status = config('enum.shipment_status')['DISPATCHED'];
                            }
                            else if (in_array($status, $pending_delivery_statuses)) {
                                $shipment_status = config('enum.shipment_status')['PENDING_DELIVERY'];
                            }
                            else if (in_array($status, $pending_return_statuses)) {
                                $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
                            }
                            else if (in_array($status, $returned_statuses)) {
                                $shipment_status = config('enum.shipment_status')['RETURNED'];
                                $order_item_status = config('enum.item_status')['RETURNED'];
                            }
                            else if (in_array($status, $delivered_statuses)) {
                                Shipment::on('mysql3')->where('id',$shipment->id)->update(['delivered_at' => $shipment_status_date, 'status' => config('enum.shipment_status')['DELIVERED']]);
                                $shipment->shipment_history(config('enum.shipment_status')['DELIVERED'], $shipment_status_date);
                                
                                $order_item_status = config('enum.item_status')['COMPLETED'];
                                $shipment_status = Null;
                            }
                            else {
                                if (!in_array('Pandago | '.$status,$notMappedStatuses)) {
                                    array_push($notMappedStatuses,'Pandago | '.$status);
                                }
                            }

                        

                            // /////// Updating SHIPMENT status if needed
                            if ($shipment_status && $shipment_status != $shipment->status) {
                                Shipment::on('mysql3')->where('id',$shipment->id)->update(['status' => $shipment_status]);
                                $shipment->shipment_history($shipment_status, ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                            }
                            
                            /////// Updating ORDER ITEM status if needed
                            if ($order_item_status) {

                                OrderItem::on('mysql3')->whereIn('id',$shipment->items->pluck('order_items_id'))->update(['status' => $order_item_status]);
                                
                                if($order_item_status == config('enum.item_status')['PENDING']) {
                                    Order::re_evaluate_status($shipment->order_id);
                                }
                            }

                            /////// Updating ORDER status if needed
                            if ($shipment->type == Null && $order_item_status && $order_item_status != config('enum.item_status')['PENDING']) {
                                Order::re_evaluate_status($shipment->order_id);
                            }

                            if (count($notMappedStatuses) > 0) {
                                foreach ($notMappedStatuses as $key => $value) {
                                    Log::info('Not Mapped Status | '.$value);
                                }
                            }
                        }
                    }
                    else{
                        Log::info('Pandago | Tracking Error | Payload either not found or might be empty');
                        return response()->json(['message' => 'Payload parameter either not found or might be empty'], 404);
                    }
                }
                else{
                    Log::info('Pandago | Tracking Error | Shipment not found');
                    return response()->json(['message' => 'Shipment not found'], 404);
                }
            }
            else{
                Log::info('Pandago | Tracking Error | Authentication Failed');
                return response()->json(['message' => 'Authentication Failed'], 404);
            }
            
        } catch (Exception $e) {
            // Mail::raw($e->getMessage(), function ($m)  {
            //     $m->to('<EMAIL>')->subject('Pandago Tracking');
            // });
            Log::info('Pandago | Tracking Error | '.$e->getTraceAsString());
            return response()->json(['message' => 'Internal error'], 500);
        }

        return response()->json(['message' => 'Success'], 200);
    }
    public static function asignTagToOrder($order_id){
        $auth_token = base64_encode(hash_hmac('sha256', $order_id, 'order_tag_assignment', true));
        $message = '';

        try{
            
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => config('enum.endpoints')['UNITY']."/api/order/assign-tag",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS =>array('order_id' => $order_id),
            CURLOPT_HTTPHEADER => array(
                "X-Auth-Token: ".$auth_token
            ),
            ));

        

            $unity_response = json_decode(curl_exec($curl),true);

            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            curl_close($curl);
            Log::info("Order Tag assignment API Response Code: ".$httpCode);

            Log::info("Order Tag assignment API Response : ");

            Log::info($unity_response);

            if($httpCode == '200')
            {
                if($unity_response['error'] == 0)
                {
                    Log::info('Order Tag assignment API - Got success from unity');
                }
                else{
                    Log::info('Order Tag assignment API - Something went wrong in response');
                }
                
            }
            else{
                Log::info('Order Tag assignment API - Response Code | '.$httpCode);
            }
        }
        catch(\Exception $e){
            $message .= 'Order Tag assingment to Unity API Error';
            $activity_id = activity()
            ->withProperties(['response' => json_encode($e->getMessage()) , 'dump' => $e->getTraceAsString()])
            ->log('Order Tag assingment');

            Log::info('Order Tag assingment to Unity API Error -  '.$e->getMessage());
        }

    }

}