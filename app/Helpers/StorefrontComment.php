<?php 

namespace App\Helpers;

use App\Models\OrderComments;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class StorefrontComment
{
    public static function add($order_id, $entity_id, $status, $message)
    {
        ///// Calling alfred API for adding Order Comment on Storefront //////
        try {

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => env('ALFRED_URL').'/api/order/status/sync',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => json_encode(compact('entity_id','status','message')),
                CURLOPT_HTTPHEADER => array( "Content-Type:application/json" )
            ));

            $response = curl_exec($curl);
            $response = json_decode($response,true);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

            if ($httpCode == '200' && $response['error'] == 0) {
                OrderComments::add($order_id, 'Storefront Synced Process', $response['message'], 'Success', 1 );
            } else {
                OrderComments::add($order_id, 'Storefront Synced Process', $response['message'], 'Failed', 1 );
            }
            
            curl_close($curl);
            
        } catch (Exception $e) {
            OrderComments::add($order_id, 'Storefront Synced Process', $e->getMessage(), 'Failed', 1 );
        }
    }
}