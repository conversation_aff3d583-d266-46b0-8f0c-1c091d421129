<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        'App\Events\ShipmentTrackingEvent' => [
            'App\Listeners\ShipmentTrackingListener',
        ],
        'App\Events\UniversalShipmentTrackingEvent' => [
            'App\Listeners\UniversalShipmentTrackingListener',
        ],
        'App\Events\KhaadiShipmentTrackingEvent' => [
            'App\Listeners\KhaadiShipmentTrackingListener',
        ],
        'App\Events\ShipmentStatusChangeEvent' => [
            'App\Listeners\ShipmentStatusChangeListener',
        ],
        'App\Events\MnPPaymentEvent' => [
            'App\Listeners\MnPPaymentListener',
        ],
        'App\Events\RiderPaymentEvent' => [
            'App\Listeners\RiderPaymentListener',
        ],
        'App\Events\TraxPaymentEvent' => [
            'App\Listeners\TraxPaymentListener',
        ],
        'App\Events\LcsPaymentEvent' => [
            'App\Listeners\LcsPaymentListener',
        ],
        'App\Events\LcsMerchantPaymentEvent' => [
            'App\Listeners\LcsMerchantPaymentListener',
        ],
        'App\Events\BlueExPaymentEvent' => [
            'App\Listeners\BlueExPaymentListener',
        ],
        'App\Events\AmazonShippingTrackingtEvent' => [
            'App\Listeners\AmazonShippingTrackingListener',
        ],
        'App\Events\DPDTrackingtEvent' => [
            'App\Listeners\DPDTrackingListener',
        ],
        'App\Events\CallCourierPaymentEvent' => [
            'App\Listeners\CallCourierPaymentListener',
        ],
        'App\Events\MoveXPaymentEvent' => [
            'App\Listeners\MoveXPaymentListener',
        ],
        \App\Events\ShopifyMarkPaidEvent::class => [
            \App\Listeners\ShopifyMarkPaidListener::class,
        ],
        'App\Events\StallionPaymentEvent' => [
            'App\Listeners\StallionPaymentListener',
        ],
        'App\Events\TCSPaymentEvent' => [
            'App\Listeners\TCSPaymentListener',
        ],
        \App\Events\ForrunShipperAdviceEvent::class => [
            \App\Listeners\ForrunShipperAdviceListener::class,
        ],
        'App\Events\LCSTrackingEvent' => [
            'App\Listeners\LCSTrackingListener',
        ],
        'App\Events\LCSMerchantTrackingEvent' => [
            'App\Listeners\LCSMerchantTrackingListener',
        ],
        'App\Events\PostExPaymentEvent' => [
            'App\Listeners\PostExPaymentListener',
        ],
        'App\Events\TCSTrackingEvent' => [
            'App\Listeners\TCSTrackingListener',
        ],
        'App\Events\BlueExTrackingEvent' => [
            'App\Listeners\BlueExTrackingListener',
        ],
        'App\Events\CallCourierTrackingEvent' => [
            'App\Listeners\CallCourierTrackingListener',
        ],
        'App\Events\TPLRiderTrackingEvent' => [
            'App\Listeners\TPLRiderTrackingListener',
        ],
        'App\Events\MnpTrackingEvent' => [
            'App\Listeners\MnpTrackingListener',
        ],
        'App\Events\PostExTrackingEvent' => [
            'App\Listeners\PostExTrackingListener',
        ],
        'App\Events\TraxTrackingEvent' => [
            'App\Listeners\TraxTrackingListener',
        ],
        'App\Events\DaewooTrackingEvent' => [
            'App\Listeners\DaewooTrackingListener',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
