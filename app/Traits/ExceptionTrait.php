<?php

namespace App\Traits;

use App\Models\Exception;
use Exception as GlobalException;

trait ExceptionTrait
{
    public static function add($seller_id, $type_id, $message, $entity_name, $entity_id, $entity_reference_id, $detail, $request_data = null, $response_data = null, $external_entity_name, $external_entity_id, $activity_generated_by = 1)
    {
        $error = 0;
        $error_message = 'Success';
        
        try {
            $temp_exception = Exception::on('mysql2')
                                ->whereSellerId($seller_id)
                                ->whereTypeId($type_id)
                                ->whereEntityName($entity_name)
                                ->whereEntityId($entity_id)
                                ->whereMessage($message)
                                ->count();

            if ($temp_exception) {
                $error = 2;
                $error_message = 'This Exceptions already exists';
            } else {
                $exception = new Exception;
                $exception->seller_id = $seller_id;
                $exception->type_id = $type_id;
                $exception->message = $message;
                $exception->entity_name = $entity_name;
                $exception->entity_id = $entity_id;
                $exception->entity_reference_id = $entity_reference_id;
                $exception->detail = $detail;
                $exception->request = $request_data;
                $exception->response = $response_data;
                $exception->external_entity_name = $external_entity_name;
                $exception->external_entity_id = $external_entity_id;
                $exception->activity_generated_by = $activity_generated_by;
                $exception->save();
                
            }
        } catch (GlobalException $e) {
            $error = 1;
            $error_message = $e->getMessage();
        }

        return ['error' => $error, 'message' => $error_message];
    }

    public function addException($seller_id, $type_id, $message, $entity_name, $entity_id, $entity_reference_id, $detail, $request_data = null, $response_data = null, $external_entity_name, $external_entity_id,  $activity_generated_by = 1)
    {
        return $this->add($seller_id, $type_id, $message, $entity_name, $entity_id, $entity_reference_id, $detail, $request_data, $response_data, $external_entity_name, $external_entity_id,  $activity_generated_by);
    }
}