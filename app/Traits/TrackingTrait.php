<?php

namespace App\Traits;

use App\Events\ForrunShipperAdviceEvent;
use App\Events\KhaadiShipmentTrackingEvent;
use App\Events\ShipmentTrackingEvent;
use App\Helpers\StorefrontComment;
use App\Models\Courier\SellerCourierAmazonShipping;
use App\Models\Courier\SellerCourierBlueEx;
use App\Models\Courier\SellerCourierCarson;
use App\Models\Courier\SellerCourierCourierX;
use App\Models\Courier\SellerCourierDaewoo;
use App\Models\Courier\SellerCourierDelybell;
use App\Models\Courier\SellerCourierDEX;
use App\Models\Courier\SellerCourierDHL;
use App\Models\Courier\SellerCourierDPD;
use App\Models\Courier\SellerCourierFlyCourier;
use App\Models\Courier\SellerCourierForrun;
use App\Models\Courier\SellerCourierLCS;
use App\Models\Courier\SellerCourierLCSMerchant;
use App\Models\Courier\SellerCourierLCSUAE;
use App\Models\Courier\SellerCourierMoveX;
use App\Models\Courier\SellerCourierQuiqup;
use App\Models\Courier\SellerCourierRider;
use App\Models\Courier\SellerCourierSwyft;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\Courier\SellerCourierPostEx;
use App\Models\Courier\SellerCourierPostExPartner;
use App\Models\Courier\SellerCourierStallion;
use App\Models\Courier\SellerCourierTimeExpress;
use App\Models\Order;
use App\Models\OrderComments;
use App\Models\OrderItem;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\ShipmentCourierHistory;
use App\Models\ShipmentWeightDescripancy;
use App\Models\WalletTransaction;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

trait TrackingTrait
{
    use LoggingTrait;
    use ExceptionTrait;

    private $client;
    private $shipment_update;
    private $order_force_update;
    private $exception_enabled;

    private $request_dump = null;
    private $response_dump = null;
    
    private $courier_name = 'Courier';
    private $courier_status_matching_by = 'in_array';
    private $log_name = 'Tracking';
    private $error = 1;
    private $message = '';
    private $overall_status = null;
    private $tracking_number = false;
    private $tracking_type;
    private $get_url = '';

    private $notMappedStatuses = [];
    private $tracking_array = [];
    private $comment_array = [];
    private $exception_array = [];
    private $logging_array = [];
    private $result_array = [];

    private $shipment;


    private function flushAllVariables()
    {
        $this->client;
        $this->shipment_update;
        $this->order_force_update;
        $this->exception_enabled;

        $this->request_dump = null;
        $this->response_dump = null;
        
        $this->courier_name = 'Courier';
        $this->courier_status_matching_by = 'in_array';
        $this->log_name = 'Tracking';
        $this->error = 1;
        $this->message = '';
        $this->overall_status = null;
        $this->tracking_number = false;
        $this->tracking_type;
        $this->get_url = '';

        $this->notMappedStatuses = [];
        $this->tracking_array = [];
        $this->comment_array = [];
        $this->exception_array = [];
        $this->logging_array = [];
        $this->result_array = [];

        $this->shipment;
    }


    private function stringMatch($string, $array, $courier_status_matching_by)
    {
        if ($courier_status_matching_by == 'in_array') {
            return in_array($string, $array);
        } else {
            foreach ($array as $value) {
                if (stripos($string,$value) !== false) {
                    return true;
                }
            }
            return false;
        }
    }
    
    private function chargeDeductedAmountAgain($shipment_status)
    {
        if (DB::connection('mysql2')->table('add_ons')->where('seller_id',$this->shipment->seller_id)->where('key','wallet')->where('value','1')->exists() &&
            $this->shipment->status == 'Cancelled' &&
            $shipment_status != 'Cancelled')  {
        
            $transaction = WalletTransaction::where('cn',$this->tracking_number)->whereOrderId($this->shipment->order_id)->whereSellerId($this->shipment->seller_id)->latest('id')->first();

            if ($transaction && $transaction->wallet_transaction_type_id == 3) {
                WalletTransaction::reCharged($transaction->amount_charged, $this->shipment->order_id, $this->shipment->seller_id, $this->tracking_number);
            }
        }
    }

    // Adding an Exception   ----------10TH CALL---------
    public function pushException($data)
    {
        $result = $this->addException($this->shipment->seller_id, $data['exception_type'], $this->log_name.' | '.$data['message'], 'Shipment', $this->shipment->id, $this->tracking_number, $data['dump'], $this->request_dump, $this->response_dump, config('unityexception.external_entity_name')['COURIER'], config('unityexception.external_entity_id')[str_replace(' ', '-', strtoupper($this->courier_name))]);
    
        if($result['error'] != 2 && $data['exception_type'] != config('unityexception.exception_types')['FALSE-RESPONSE']) {

            $html = $data['message'].' | Seller ID = '.$this->shipment->seller_id.' | Tracking Number (CN/AWB) = '.$this->tracking_number.' | '.$this->get_url;
            
            // if ($this->shipment->seller_id == 119) {
            //     Mail::send([], [], function (Message $message) use ($html) {
            //         $message->to(['<EMAIL>','<EMAIL>'])
            //         ->bcc('<EMAIL>')
            //         ->subject('Khaadi Courier Exception | '.$this->courier_name)
            //         ->setBody($html, 'text/html');
            //     });
            // } else {
                // Mail::send([], [], function (Message $message) use ($html) {
                //     $message->to('<EMAIL>')
                //     ->subject($this->log_name)
                //     ->setBody($html, 'text/html');
                // });
            // }
            

        }

        if ($result['error']) {
            return false;
        } else {
            return true;
        }
    }



    // checking shipment courier and calling their function  ----------3RD CALL---------
    private function getResponseFromCourier()
    {
        $courier_id = $this->shipment->courier_id;

        if ($courier_id == 4) {
            $this->mnp();
        } else if ($courier_id == 5) {
            $this->lcs();
        } else if ($courier_id == 7) {
            $this->callCourier();
        } else if ($courier_id == 8) {
            $this->trax();
        } else if ($courier_id == 10) {
            $this->blueEx();
        } else if ($courier_id == 11) {
            $this->rider();
        } else if ($courier_id == 12) {
            $this->deliveryExpress();
        } else if ($courier_id == 13) {
            $this->tcs();
        } else if ($courier_id == 14) {
            $this->lcsUAE();
        } else if ($courier_id == 15) {
            $this->swyft();
        } else if ($courier_id == 16) {
            $this->dpd();
        } else if ($courier_id == 17) {
            $this->dhl();
         }else if ($courier_id == 18) {
            $this->courierx();
        } else if ($courier_id == 19) {
            $this->movex();
        } else if ($courier_id == 21) {
            $this->carson();
        } else if($courier_id == 22) {
            $this->delybell();
        } else if($courier_id == 23) {
            $this->quiqup();
        } else if($courier_id == 27) {
            $this->forrun();
        } else if($courier_id == 28) {
            $this->postex();
        } else if($courier_id == 29) {
            $this->stallion();
        } else if($courier_id == 30) {
            $this->timeExpress();
        } else if($courier_id == 33) {
            $this->tcsuae();
        } else if($courier_id == 34) {
            $this->daewoo();
        } else if($courier_id == 35) {
            if($courier_id == 35 &&  $this->shipment->seller_id == 4933 && $this->shipment->created_at < Carbon::create(2024, 11, 17) ) { //temporary code for svesotn FLH-2025
                Log::info("Sveston LCS check for ".$this->shipment->tracking_number);
                $this->lcs_merchant_sveston();
            }else{
                $this->lcs_merchant();
            }
        } else if($courier_id == 36) {
            $this->fly_courier();
        } else if($courier_id == 38) {
            $this->postex_partner();
        } else if($courier_id == 39) {
            $this->tqs();
        } else if($courier_id == 40) {
            $this->dex();
        } else {
            $this->message = 'Courier ID #'.$courier_id.' tracking not found of shipment #'.$this->shipment->tracking_number;
        }
    }



    // checking for permission to processing tracking details otherwise just return response  ----------5TH CALL---------
    private function startProcessShipment()
    {
        if ($this->error == 0) {        // if no error then proceed

            if ($this->shipment_update || $this->order_force_update || $this->exception_enabled) {
                $this->processingShipmentData();
            } else {
                $this->result_array['shipment_update'] = false;
                $this->result_array['tracking_data'] = $this->tracking_array;
            }

        } else {                        // if error then log it
            array_push($this->logging_array , $this->message);
            array_push($this->exception_array , ['message' => $this->message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['FALSE-RESPONSE']]);
        }
        
        return true;
    }



    // processing tracking array / tracking details of shipment  ----------6TH CALL---------
    private function processingShipmentData()
    {
        if (empty($this->tracking_array)) {             // checking that array has tracking data or not
            return 'tracking data is empty';
        } else {

            $shipment_last_run = $this->shipment->last_run;
            $on_way_to_return = false;
            $terminal = false;
            $shipment_courier_history_array = [];
            $shipment_status = null;
            $order_item_status = null;
            $shipment_delivered_date = null;
            $shipment_status_date = null;

            if($this->shipment->type == 'reverse') {
                Log::info('checking status for reverse shipment');
                $slug_courier_name = 'reverse-'.str_replace(' ', '-', strtolower($this->courier_name));
            } elseif($this->shipment->type == null) {
                $slug_courier_name = str_replace(' ', '-', strtolower($this->courier_name));
            }

            $booked_statuses = config('courierstatuses.'.$slug_courier_name)['BOOKED'];
            $dispatched_statuses = config('courierstatuses.'.$slug_courier_name)['DISPATCHED'];
            $pending_delivery_statuses = config('courierstatuses.'.$slug_courier_name)['PENDING-DELIVERY'];
            $pending_return_statuses = config('courierstatuses.'.$slug_courier_name)['PENDING-RETURN'];
            $delivered_statuses = config('courierstatuses.'.$slug_courier_name)['DELIVERED'];
            $returned_statuses = config('courierstatuses.'.$slug_courier_name)['RETURNED'];
            $cancelled_statuses = config('courierstatuses.'.$slug_courier_name)['CANCELLED'];
            $lost_statuses = config('courierstatuses.'.$slug_courier_name)['LOST'];

            foreach ($this->tracking_array as $key => $value) {

                if (isset($value['status'])) {
                    
                    if ($key && $this->exception_enabled && Carbon::parse($shipment_status_date)->gt($value['status_date'])) {
                        if ($value['status'] != $status) {
                            array_push($this->exception_array , ['message' => 'Timestamp of <b>'.$value['status'].'</b> status <b>('.$value['status_date'].')</b> is earlier than its previous status <b>'.$status.'</b> timestamp <b>('.$shipment_status_date.')</b>  shipment #'.$this->tracking_number, 'dump' => '', 'exception_type' => config('unityexception.exception_types')['STATUS-TIMESTAMP-IS-SMALLER']]);
                        }
                    }
                    
                    $status = $value['status'];
                    $shipment_status_date = $value['status_date'];


                    // Checking Last run / Last sync
                    if (in_array($this->tracking_type, [config('enum.tracking_types')['1DAY-RE-TRACK'],
                                                        config('enum.tracking_types')['3DAY-RE-TRACK'],
                                                        config('enum.tracking_types')['7DAY-RE-TRACK'],
                                                        config('enum.tracking_types')['FORCE-TRACK']
                                                        ] )  || $shipment_status_date->gte($shipment_last_run) ) {

                        $shipment_status_date = $shipment_status_date->toDateTimeString();

                        // Checking for another status after terminal status -----START-----
                        if ($this->exception_enabled && $terminal) {
                            array_push($this->exception_array , ['message' => $status.' status exists after terminal status against '.$this->courier_name.' shipment #'.$this->tracking_number, 'dump' => '', 'exception_type' => config('unityexception.exception_types')['EXTRA-STATUS-AFTER-TERMINAL']]);
                            
                            $temp_status_exists = ShipmentCourierHistory::on('mysql2')->where('shipment_id',$this->shipment->id)->where('status',$status)->where('status_at',$shipment_status_date);

                            if ($this->tracking_type == config('enum.tracking_types')['NORMAL']) {
                                break;
                            } elseif ($this->tracking_type == config('enum.tracking_types')['7DAY-RE-TRACK'] && !$temp_status_exists->exists()) {
                                $this->forceTrackStatus('Another new status ('.$status.') has been found while tracking <b>'.$this->shipment->status.'</b> Shipment #'.$this->shipment->tracking_number. ' after 7 days of courier <b>'.$this->courier_name.'</b> triggering <b>Force Tracking</b> on this');
                            } elseif ($this->tracking_type == config('enum.tracking_types')['3DAY-RE-TRACK'] && !$temp_status_exists->exists()) {
                                $this->forceTrackStatus('Another new status ('.$status.') has been found while tracking <b>'.$this->shipment->status.'</b> Shipment #'.$this->shipment->tracking_number. ' after 3 days of courier <b>'.$this->courier_name.'</b> triggering <b>Force Tracking</b> on this');
                            } elseif ($this->tracking_type == config('enum.tracking_types')['1DAY-RE-TRACK'] && !$temp_status_exists->exists()) {
                                $this->forceTrackStatus('Another new status ('.$status.') has been found while tracking <b>'.$this->shipment->status.'</b> Shipment #'.$this->shipment->tracking_number. ' after 1 day of courier <b>'.$this->courier_name.'</b> triggering <b>Force Tracking</b> on this');
                            }
                        }
                        // Checking for another status after terminal status -----END-----
        
                        if (in_array($status, config('courierstatuses.'.$slug_courier_name)['WAY-TO-RETURN'])) {
                            $on_way_to_return = true;
                        }

                        if ($this->stringMatch($status, config('courierstatuses.'.$slug_courier_name)['WAY-TO-RETURN'],'stringMatch')) {
                            $on_way_to_return = true;
                        }

                        $detail = exists_or_null($value,'message','-');
                        $shipment_courier_history_array[] = compact('status','shipment_status_date','detail');
        
                        // Status Checking -----START-----
                        $temp_result = $this->checkMatchingStatus(  $status,
                                                                    $shipment_status_date,
                                                                    $returned_statuses,
                                                                    $cancelled_statuses,
                                                                    $lost_statuses,
                                                                    $delivered_statuses,
                                                                    $booked_statuses,
                                                                    $dispatched_statuses,
                                                                    $pending_delivery_statuses,
                                                                    $pending_return_statuses,

                                                                    $shipment_status,
                                                                    $shipment_delivered_date,
                                                                    $order_item_status,
                                                                    $terminal,
                                                                    $on_way_to_return
                                                                );

                        $shipment_status = $temp_result['shipment_status'];
                        $order_item_status = $temp_result['order_item_status'];
                        $shipment_delivered_date = $temp_result['shipment_delivered_date'];
                        $terminal = $temp_result['terminal'];
                        // Status Checking -----END-----

                    }
                } else {
                    // tracking status not found
                }

            }

            // Checking for overall mismatch status EXCEPTION -----START-----
            if (!$terminal && $shipment_status && $this->overall_status) {

                $temp_result = $this->checkMatchingStatus(  $this->overall_status,
                                                            null,
                                                            $returned_statuses,
                                                            $cancelled_statuses,
                                                            $lost_statuses,
                                                            $delivered_statuses
                                                        );

                $overall_shipment_status = $temp_result['shipment_status'];
                $order_item_status = $temp_result['order_item_status'];

                if ($overall_shipment_status) {
                    $temp_exception_message = 'Tracking Details doesn\'t contain any '.$overall_shipment_status.' status against this shipment, but '.$this->courier_name.' shipment #'.$this->tracking_number.' overall status is '.$this->overall_status;
                    $shipment_courier_history_array[] = ['status' => $this->overall_status, 'shipment_status_date' => Carbon::now()->toDateTimeString(), 'detail' => $temp_exception_message ];
                    $shipment_status = $overall_shipment_status;
                    if ($this->exception_enabled) {
                        array_push($this->exception_array , ['message' => $temp_exception_message, 'dump' => '', 'exception_type' => config('unityexception.exception_types')['OVERALL-TERMINAL-STATUS-MISMATCH']]);
                    }
                }
            }
            // Checking for overall mismatch status -----END-----

            if (count($this->notMappedStatuses) > 0) {
                foreach ($this->notMappedStatuses as $key => $value) {
                    Log::info('Not Mapped Status | '.$value.' | '.$this->tracking_number);
                }
            }

            return $this->updateDetails($shipment_status, $order_item_status, $shipment_delivered_date, $shipment_status_date, $shipment_courier_history_array);
        }

    }



    // parsing all statuses of courier and finding group of match status    ----------7TH CALL---------
    private function checkMatchingStatus(   $status,
                                            $shipment_status_date,
                                            $returned_statuses=[],
                                            $cancelled_statuses=[],
                                            $lost_statuses=[],
                                            $delivered_statuses=[],
                                            $booked_statuses=[],
                                            $dispatched_statuses=[],
                                            $pending_delivery_statuses=[],
                                            $pending_return_statuses=[],
                                            
                                            $shipment_status=null,
                                            $shipment_delivered_date=null,
                                            $order_item_status=null,
                                            $terminal=false,
                                            $on_way_to_return=false
                                        )
    {
        $courier_status_matching_by = $this->courier_status_matching_by;

        if ($this->stringMatch($status, $booked_statuses, $courier_status_matching_by)) {
            // Ignore
        } else if ($this->stringMatch($status, $dispatched_statuses, $courier_status_matching_by)) {
            $shipment_status = config('enum.shipment_status')['DISPATCHED'];
        } else if ($this->stringMatch($status, $pending_delivery_statuses, $courier_status_matching_by)) {
            $shipment_status = config('enum.shipment_status')['PENDING_DELIVERY'];

            if ( $on_way_to_return && $this->stringMatch($status, config('courierstatuses.'.str_replace(' ', '-', strtolower($this->courier_name)))['SOMETIMES-PENDING-RETURN'], $courier_status_matching_by)) {
                $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
            }
        } else if ($this->stringMatch($status, $pending_return_statuses, $courier_status_matching_by)) {
            $shipment_status = config('enum.shipment_status')['PENDING_RETURN'];
        } else if ($this->stringMatch($status, $returned_statuses, $courier_status_matching_by)) {
            $shipment_status = config('enum.shipment_status')['RETURNED'];
            $order_item_status = config('enum.item_status')['RETURNED'];
            $terminal  = true;
        } else if ($this->stringMatch($status, $cancelled_statuses, $courier_status_matching_by) || $status == 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED']) {

            if ($this->shipment->courier_id = 8 && $this->shipment->seller_id == env('JDOT_SELLER_ID', 4950) && !in_array( $this->shipment->status , ['Booked', 'Ready for Dispatch'])) {
                Log::info('Cancelled status received on jdot shipment', $this->shipment->toArray() );

            } else {

                $shipment_status = config('enum.shipment_status')['CANCELLED'];
                $order_item_status = config('enum.item_status')['PENDING'];
                $terminal  = true;
            }

        } else if ($this->stringMatch($status, $lost_statuses, $courier_status_matching_by)) {
            $setting = null;
            $setting = Setting::where('seller_id',$this->shipment->seller_id)->where('key','lostOrderStatus')->first();
            if(!isset($setting)){
                $order_item_status = config('enum.item_status')['PENDING'];
            } else{
                if($setting->value == 'completed'){
                    $order_item_status = config('enum.item_status')['COMPLETED'];
                } else{
                    $order_item_status = config('enum.item_status')['PENDING'];
                }
            }
            $shipment_status = config('enum.shipment_status')['LOST'];
            $terminal  = true;
        } else if ($this->stringMatch($status, $delivered_statuses, $courier_status_matching_by)) {
            $shipment_status = config('enum.shipment_status')['DELIVERED'];
            $order_item_status = config('enum.item_status')['COMPLETED'];
            $shipment_delivered_date = $shipment_status_date;
            $terminal  = true;
        } else {
            if (!in_array($this->courier_name.' | '.$status,$this->notMappedStatuses)) {
                array_push($this->notMappedStatuses,$this->courier_name.' | '.$status. ($this->overall_status === $status ? ($shipment_status_date ? '' : ' | Overall Status') : ''));
            }
        }

        return compact('shipment_status','order_item_status','shipment_delivered_date','terminal');
    }



    // updating shipment and order statuses  ----------8TH CALL---------
    private function updateDetails($shipment_status, $order_item_status, $shipment_delivered_date, $shipment_status_date, $shipment_courier_history_array)
    {
        // Updating shipment and further if needed
        if ($this->shipment_update) {

            foreach ($shipment_courier_history_array as $value) {
                $this->shipment->shipment_courier_history($value['status'], ($value['shipment_status_date'] ? $value['shipment_status_date'] : Carbon::now()->toDateTimeString()), $value['detail'] );
            }

            if ($this->shipment->status == config('enum.shipment_status')['RETURNED_RECEIVED']) {
                $this->shipment->status = config('enum.shipment_status')['RETURNED'];
            }
            
            // Updating SHIPMENT status if needed
            if ($shipment_status && $shipment_status != $this->shipment->status) {
                
                if ($shipment_status ==  config('enum.shipment_status')['DELIVERED']) {
                    Shipment::on('mysql3')->where('id',$this->shipment->id)->update(['delivered_at' => $shipment_delivered_date, 'status' => $shipment_status]);
                    
                    // Paid shipments or shipments which have cod < 1
                    // Now this is happening at the time of delivery status sync

                    // if ($this->shipment->seller_id == 119) {
                    //     if(Shipment::whereId($this->shipment->id)->value('cod') < 1){
                    //         $order = Order::whereId($this->shipment->order_id)->first(['entity_id']);
                    //         if($order){
                    //             StorefrontComment::add($this->shipment->order_id, $order->entity_id, 'complete_instore', 'Payment settled by '.$this->courier_name);   
                    //         }
                    //     }
                    // }
                } else {
                    Shipment::on('mysql3')->where('id',$this->shipment->id)->update(['status' => $shipment_status]);
                }

                $this->chargeDeductedAmountAgain($shipment_status);
                $this->result_array['shipment_update'] = 'Shipment #'.$this->tracking_number.' successfully updated ('.$shipment_status.')';
                $this->shipment->shipment_history($shipment_status, ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
            }

            if (!$order_item_status && $this->tracking_type == config('enum.tracking_types')['FORCE-TRACK']) {
                $order_item_status = config('enum.item_status')['DISPATCHED'];
            }
            
            // Updating ORDER ITEM status if needed
            if ($order_item_status) {
                OrderItem::on('mysql3')->whereIn('id',$this->shipment->items->pluck('order_items_id'))->update(['status' => $order_item_status]);
                
                // Not Updating ORDER status for reverse shipments needed
                if ($this->shipment->type != Null) {
                    $order_item_status = null;
                }
            }

        }

        // FORCE Updating ORDER status if needed
        if($this->order_force_update || $order_item_status) {
            $temp_result = Order::re_evaluate_status($this->shipment->order_id);
            
            if ($temp_result['error']) {
                $this->result_array['order_update_error'] = $temp_result['message'];
            } else {
                $this->result_array['order_update'] = $temp_result['message'];
            }
        }
        
        return true;
        
    }



    // adding logs and exception if allowed or present   ----------9TH CALL---------
    private function processErrorsLogsComments($logging_enabled)
    {
        try {



            // If EXCEPTION enabled then generate all exception
            if ($this->exception_enabled) {
                $exception_count = 0;
                foreach ($this->exception_array as $key => $value) {
                    ( $this->pushException($value) ? $exception_count++ : $exception_count );
                }
                $this->result_array['exception'] = $exception_count.' exception(s) added';
                if (count($this->exception_array)) {
                    $this->result_array['total_exception'] = count($this->exception_array).' exception(s) generated';
                    Log::info($this->exception_array);
                }
            }




            // If LOGGING enabled then generate all logs
            if ($logging_enabled) {
                $this->addArrayLog($this->logging_array, $this->shipment->seller_id, $this->shipment, $this->log_name, json_encode(['request_dump' => $this->request_dump, 'response_dump' => $this->response_dump]));
                $this->result_array['activity_log'] = count($this->logging_array).' log(s) added';
            }




            // If SHIPMENT UPDATE enabled then add all ORDER COMMENTS
            if ($this->shipment_update) {

                foreach ($this->comment_array as $value) {
                    OrderComments::add($this->shipment->order_id, 'Shipment Tracking Process', $value['message'], $value['status'], 1);
                }
                $this->result_array['comments'] = count($this->comment_array).' comments(s) added';
            }




            $this->result_array['message'] = $this->message;
            $this->result_array['error'] = $this->error;

        } catch (Exception $e) {
            $this->result_array['message'] = 'Error occurs while processing logs and exceptions | '.$e->getMessage();
            $this->result_array['error'] = 1;
        }
        $this->result_array['tracking_number'] = $this->tracking_number;
        $this->result_array['courier'] = $this->courier_name;
        $this->result_array['seller_id'] = $this->shipment->seller_id;
        $this->result_array['last_run'] = $this->shipment->last_run;
    }



    // MAIN function which call others function  ----------2ND CALL---------
    public function trackShipment(object $shipment,
                                  $shipment_update = false,
                                  $order_force_update = false,
                                  $exception_enabled = false,
                                  $logging_enabled = false)
    {
        try {
            $this->shipment_update = $shipment_update;
            $this->order_force_update = $order_force_update;
            $this->exception_enabled = $exception_enabled;
            $this->tracking_number = $shipment->tracking_number;
            $this->shipment = $shipment;
            $this->client = new Client([
                'timeout' => 60, // Response timeout
                'connect_timeout' => 30, // Connection timeout
            ]);

            // Getting Tracking Response From Courier API
            $this->getResponseFromCourier();

            // Process Shipment Tracking Data   
            $this->startProcessShipment();
            
        } catch(RequestException $e) {

            if (method_exists($e, 'getResponse')) {
                if ($e->getResponse()) {
                    $error_response = json_decode(((string) $e->getResponse()->getBody()), true);

                    if ( isset($error_response['error']) ) {
                        $temp_error = $error_response['error'];
                    } elseif ( isset($error_response['Error']) ) {
                        $temp_error = json_encode($error_response['Error']);
                    } elseif ( isset($error_response['message']) ) {
                        $temp_error = json_encode($error_response['message']);
                    } elseif ( isset($error_response['Message']) ) {
                        $temp_error = json_encode($error_response['Message']);
                    } else {
                        $temp_error = $e->getMessage();
                    }
                } else {
                    $temp_error = $e->getMessage();
                }
            } else {
                $temp_error = $e->getMessage();
            }

            $this->message .= 'Got Request Exception Error On Tracking Request : '.$temp_error;
            $this->error = 1;
            array_push($this->logging_array , ['message' => $this->message, 'skip_trace' => $e->getTraceAsString()]);
            
        } catch(Exception $e) {
            $this->message .= 'Got Exception Error On Tracking Request : '.$e->getMessage();
            $this->error = 1;
            array_push($this->logging_array , ['message' => $this->message, 'skip_trace' => $e->getTraceAsString()]);
        }

        // adding all logs and exception generating by above code ONLY if allowed
        $this->processErrorsLogsComments($logging_enabled);

        return $this->result_array;
    }



    // setting tracking option according to its type  ----------1ST CALL---------
    public function trackSingle(object $shipment, int $type = 1)
    {
        $this->flushAllVariables();
        $this->tracking_type = $type;
        
        if ($shipment->status == config('enum.shipment_status')['RETURNED_RECEIVED']) {
            return ['error' => 1, 'message' => 'Shipment status is already '.config('enum.shipment_status')['RETURNED_RECEIVED']];

        } elseif ($type == config('enum.tracking_types')['NORMAL'] && $shipment->status != config('enum.shipment_status')['CANCELLED']) {
            $this->log_name = 'NORMAL TRACK';
            return $this->trackShipment($shipment, true, false, true, true);

        } elseif ($type == config('enum.tracking_types')['FORCE-TRACK']) {
            $this->log_name = 'FORCE TRACK';
            $result = $this->trackShipment($shipment, true, true, true, true);

            $temp_message = array_map(
                                function($value, $key) {
                                    if ( ! in_array($key, ['total_exception','seller_id','error','comments','activity_log','order_update_error']) ) {
                                        return '<b>'.ucwords(str_replace('_',' ',$key)).'</b> : '.$value.' <br>';
                                    }
                                },
                                array_values($result), array_keys($result)
                            );
            $temp_message = implode(' ', $temp_message);

            OrderComments::add($shipment->order_id, 'Shipment Force Tracking', $temp_message, ( $result['error'] ? 'Failed' : 'Success' ), 1);
            return $result;

        } elseif ($type == config('enum.tracking_types')['1DAY-RE-TRACK']) {
            $this->log_name = '1 DAY RE TRACK';
            return $this->trackShipment($shipment, false, false, true, true);

        } elseif ($type == config('enum.tracking_types')['3DAY-RE-TRACK']) {
            $this->log_name = '3 DAY RE TRACK';
            return $this->trackShipment($shipment, false, false, true, true);

        } elseif ($type == config('enum.tracking_types')['7DAY-RE-TRACK']) {
            $this->log_name = '7 DAY RE TRACK';
            return $this->trackShipment($shipment, false, false, true, true);

        } elseif ($type == config('enum.tracking_types')['ONLY-RESPONSE']) {
            $this->log_name = 'ONLY RESPONSE';
            return $this->trackShipment($shipment);

        } else {
            return ['error' => 1, 'message' => $type.' is invalid'];
        }
        
    }



    /// Sometimes needed for FORCE TRACK Shipment 
    private function forceTrackStatus($message)
    {
        $enterprise = in_array($this->shipment->seller_id,[119,120]) ? 1 : 0;
        
        if ($enterprise == 0) {
            Log::info("forceTrackStatus >>> ".$this->shipment->id);
            event(new ShipmentTrackingEvent([$this->shipment->id], 2));
        } elseif ($enterprise == 1) {
            event(new KhaadiShipmentTrackingEvent([$this->shipment->id], 2));
        }
        OrderComments::add($this->shipment->order_id, 'Shipment Force Tracking', $message, 'Success', '1' );
    }





    // Calling MnP API to get response and reading response   ----------4TH CALL---------
    private function mnp()
    {
        $url = 'http://mnpcourier.com/mycodapi/api/Tracking/Tracking?consignment='.$this->tracking_number;
        $this->request_dump = $url;
        $this->get_url = $url;
        $this->courier_name = 'MnP';
        $this->log_name = 'MnP '.$this->log_name;
        $message = '';

        $response = $this->client->get($url); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response[0]['isSuccess']) && $response[0]['isSuccess'] == 'true') {

            if (empty($response[0]['tracking_Details'][0]['Details'])) {
                $error = 0;
                $message = 'Tracking Details not found against this shipment #'.$this->tracking_number.', shipment overall status is used | '.$response[0]['tracking_Details'][0]['CNStatus'];
                array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), $response[0]['tracking_Details'][0]['CNStatus'] ) );
            } else {

                foreach (array_reverse($response[0]['tracking_Details'][0]['Details']) as $key => $detail) {

                    if ($detail) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::parse($detail['DateTime']);
                        $message = 'Last Courier Status : '.$detail['Status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['Detail'], $temp_shipment_status_date, $detail['Status'] ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }
            }

            $this->overall_status = isset($response[0]['tracking_Details'][0]['CNStatus']) ? $response[0]['tracking_Details'][0]['CNStatus'] : null;

        } else {
            if (isset($response[0]['message'])) {
                $message .= 'MnP Response Message : '.$response[0]['message'];
                
                if ($response[0]['message'] == 'Invalid Consignment Number,Kindly Remove this consigment number:  then try again.') {
                    
                    if (($this->shipment->created_at->diffInDays(Carbon::now())) < 30) {
                        $this->exception_enabled = false;
                        $message .= $response[0]['message'].' | Not older then 30 days';
                    } else if (($this->shipment->created_at->diffInDays(Carbon::now())) > 120) {

                        // $error = 0;
                        // $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days | '.$this->message;
                        // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED'] ) );
                        // array_push($this->comment_array , ['message' => $message, 'status' => 'Failed']);
                        // array_push($this->exception_array , ['message' => $message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['INVALID-SHIPMENT-120']]);
                        $message .= 'Got Exception Error On Tracking Request : ';
                        $error = 1;
                    } else {
                        $message = $response[0]['message'];
                    }
                }

            } else {
                $message .= 'IsSuccess in Response contains False';
            }
        }

        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling LCS API to get response and reading response   ----------4TH CALL---------
    private function lcs()
    {
        $this->courier_name = 'LCS';
        $this->log_name = 'LCS '.$this->log_name;
        $this->courier_status_matching_by = 'stringMatch';
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $api_key = DB::connection('mysql2')->table('admin_settings')->where('key','lcs_api_key')->value('value');
            $api_password = DB::connection('mysql2')->table('admin_settings')->where('key','lcs_api_password')->value('value');
            $api_url = "https://merchantapi.leopardscourier.com/api/trackBookedPacket/format/json";

            if ($api_key && $api_password) {
                $lcs = new SellerCourierLCS;
                $lcs->api_key = $api_key;
                $lcs->api_password = $api_password;
            } else {
                $this->message = $this->log_name.' | LCS Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $lcs = SellerCourierLCS::where('seller_id', $this->shipment->seller_id)->first();
            $api_url = "http://new.leopardscod.com/webservice/trackBookedPacket/format/json";
        }
        
        // $lcs = SellerCourierLCS::where('seller_id', $this->shipment->seller_id)->first();
        
        if (empty($lcs)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $params =   [   'api_key' => $lcs->api_key,
                        'api_password' => $lcs->api_password,
                        'track_numbers' => $this->tracking_number ];
        $this->request_dump = json_encode(['url' => $api_url, 'form_params' => $params]);

        $response = $this->client->post($api_url, [
            'http_errors' => FALSE,
            'form_params' => $params
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['error']) && $response['error'] == 0) {
            
            if (isset($response['packet_list'][0]['Tracking Detail']) && !empty($response['packet_list'][0]['Tracking Detail'])) {
                $details = array_filter($response['packet_list'][0]['Tracking Detail'], function($detail) {
                    return (!empty($detail['Activity_Date']));
                });
    
                usort($details, function ($a, $b) {
                    $a = new Carbon($a['Activity_Date']);
                    $b = new Carbon($b['Activity_Date']);
                    return $a->gt($b);
                });
    
                foreach ($details as $key => $detail) {
    
                    try {
                        $temp_shipment_status_date = Carbon::parse($detail['Activity_datetime']);
                    } catch (Exception $e) {
                        $temp_shipment_status_date = Carbon::parse($detail['Activity_Date']);
                    }
    
                    if ($detail) {
                        $error = 0;
                        $message = 'Last Courier Status : '.$detail['Status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, ( isset($detail['Reciever_Name']) ? 'Receiver Name : '.$detail['Reciever_Name'].' | ' : ' ').( isset($detail['Reason']) ? $detail['Reason'] : '-'), $temp_shipment_status_date, $detail['Status'] ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }

                // to store booked and actual weight received from courier
                if ( isset($response['packet_list'][0]['booked_packet_weight']) && isset($response['packet_list'][0]['arival_dispatch_weight']) ){
                    // ShipmentWeightDescripancy::add($this->shipment->seller_id,$this->shipment->id,$this->shipment->weight,$response['packet_list'][0]['booked_packet_weight'],$response['packet_list'][0]['arival_dispatch_weight']);
                    ShipmentWeightDescripancy::updateOrCreate(['seller_id' => $this->shipment->seller_id, 'shipment_id' =>  $this->shipment->id], ['seller_id' =>  $this->shipment->seller_id , 'shipment_id' =>  $this->shipment->id , 'original_weight' =>  $this->shipment->weight , 'courier_booked_weight' =>  $response['packet_list'][0]['booked_packet_weight'] , 'courier_arival_weight' =>  $response['packet_list'][0]['arival_dispatch_weight'] ]);
                }


            } elseif (isset($response['packet_list'][0]['booked_packet_status'])) {
                $error = 0;
                $message = 'Tracking Details not found against this shipment #'.$this->tracking_number.', shipment overall status is used | '.$response['packet_list'][0]['booked_packet_status'];
                array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), $response['packet_list'][0]['booked_packet_status'] ) );
                
            } else {
                $message = 'Tracking Details Not Found and error not found | '.json_encode($response);
            }

            $this->overall_status = isset($response['packet_list'][0]['booked_packet_status']) ? $response['packet_list'][0]['booked_packet_status'] : null;

        } else if(isset($response['error'])) {
            
            if ($response['error'] === 'Invalid API Key/Password' && ($this->shipment->created_at->diffInDays(Carbon::now())) > 120) {
                
                // $error = 0;
                // $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days.';
                // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED'] ) );
                // array_push($this->comment_array , ['message' => $message, 'status' => 'Failed']);
                // array_push($this->exception_array , ['message' => $message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['INVALID-SHIPMENT-120']]);
                $message .= 'Got Exception Error On Tracking Request : Invalid API Key/Password';
                $error = 1;  
            } else {
                $message = $response['error'];
            }
        } else {
            $message = 'Tracking Details Not Found | '.json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling LCS Merchant API to get response and reading response  
    private function lcs_merchant()
    {
        $this->courier_name = 'LCS_Merchant';
        $this->log_name = 'LCS Merchant '.$this->log_name;
        $this->courier_status_matching_by = 'stringMatch';
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $api_key = DB::connection('mysql2')->table('admin_settings')->where('key','lcs_merchant_api_key')->value('value');
            $api_password = DB::connection('mysql2')->table('admin_settings')->where('key','lcs_merchant_api_password')->value('value');

            if ($api_key && $api_password) {
                $lcs = new SellerCourierLCSMerchant();
                $lcs->api_key = $api_key;
                $lcs->api_password = $api_password;
            } else {
                $this->message = $this->log_name.' | LCS Merchant Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $lcs = SellerCourierLCSMerchant::where('seller_id', $this->shipment->seller_id)->first();
        }
        
        // $lcs = SellerCourierLCS::where('seller_id', $this->shipment->seller_id)->first();
        
        if (empty($lcs)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $params =   [   'api_key' => $lcs->api_key,
                        'api_password' => $lcs->api_password,
                        'track_numbers' => $this->tracking_number ];
        $this->request_dump = json_encode(['url' => 'https://merchantapi.leopardscourier.com/api/trackBookedPacket/format/json', 'form_params' => $params]);

        $response = $this->client->post('https://merchantapi.leopardscourier.com/api/trackBookedPacket/format/json', [
            'http_errors' => FALSE,
            'form_params' => $params
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;


        // Reading response data
        if (isset($response['error']) && $response['error'] == 0) {
            if (isset($response['packet_list'][0]['Tracking Detail']) && !empty($response['packet_list'][0]['Tracking Detail'])) {
                $details = array_filter($response['packet_list'][0]['Tracking Detail'], function($detail) {
                    return (!empty($detail['Activity_Date']));
                });
    
                usort($details, function ($a, $b) {
                    $a = new Carbon($a['Activity_Date']);
                    $b = new Carbon($b['Activity_Date']);
                    return $a->gt($b);
                });

                foreach ($details as $key => $detail) {
    
                    try {
                        $temp_shipment_status_date = Carbon::parse($detail['Activity_datetime']);
                    } catch (Exception $e) {
                        $temp_shipment_status_date = Carbon::parse($detail['Activity_Date']);
                    }
    
                    if ($detail) {

                        $error = 0;
                        $message = 'Last Courier Status : '.$detail['Status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, ( isset($detail['Reciever_Name']) ? 'Receiver Name : '.$detail['Reciever_Name'].' | ' : ' ').( isset($detail['Reason']) ? $detail['Reason'] : '-'), $temp_shipment_status_date, $detail['Status'] ) );
                    } else {

                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }

                // to store booked and actual weight received from courier
                if ( isset($response['packet_list'][0]['booked_packet_weight']) && isset($response['packet_list'][0]['arival_dispatch_weight']) ){
                    // ShipmentWeightDescripancy::add($this->shipment->seller_id,$this->shipment->id,$this->shipment->weight,$response['packet_list'][0]['booked_packet_weight'],$response['packet_list'][0]['arival_dispatch_weight']);
                    ShipmentWeightDescripancy::updateOrCreate(['seller_id' => $this->shipment->seller_id, 'shipment_id' =>  $this->shipment->id], ['seller_id' =>  $this->shipment->seller_id , 'shipment_id' =>  $this->shipment->id , 'original_weight' =>  $this->shipment->weight , 'courier_booked_weight' =>  $response['packet_list'][0]['booked_packet_weight'] , 'courier_arival_weight' =>  $response['packet_list'][0]['arival_dispatch_weight'] ]);
                }


            } elseif (isset($response['packet_list'][0]['booked_packet_status'])) {

                $error = 0;
                $message = 'Tracking Details not found against this shipment #'.$this->tracking_number.', shipment overall status is used | '.$response['packet_list'][0]['booked_packet_status'];
                array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), $response['packet_list'][0]['booked_packet_status'] ) );
                
            } else {
                $message = 'Tracking Details Not Found and error not found | '.json_encode($response);
            }
            $this->overall_status = isset($response['packet_list'][0]['booked_packet_status']) ? $response['packet_list'][0]['booked_packet_status'] : null;

        } else if(isset($response['error'])) {
            
            if ($response['error'] === 'Invalid API Key/Password' && ($this->shipment->created_at->diffInDays(Carbon::now())) > 120) {
                
                // $error = 0;
                // $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days.';
                // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED'] ) );
                // array_push($this->comment_array , ['message' => $message, 'status' => 'Failed']);
                // array_push($this->exception_array , ['message' => $message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['INVALID-SHIPMENT-120']]);
                $message .= 'Got Exception Error On Tracking Request : Invalid API Key/Password';
                $error = 1;
            } else {
                $message = $response['error'];
            }
        } else {
            $message = 'Tracking Details Not Found | '.json_encode($response);
        }

        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // This is a temporary function FLH-2025 
    private function lcs_merchant_sveston()
    {
            
        Log::info("LCS Merchant Sveston started <".$this->tracking_number.">");

        $this->courier_name = 'LCS_Merchant';
        $this->log_name = 'LCS Merchant '.$this->log_name;
        $this->courier_status_matching_by = 'stringMatch';
        $message = '';

        $params =   [   'api_key' => '91BB7F2E7AFD6041EC490F84B1685231',
                        'api_password' => 'SVESTON123',
                        'track_numbers' => $this->tracking_number ];
        $this->request_dump = json_encode(['url' => 'https://merchantapi.leopardscourier.com/api/trackBookedPacket/format/json', 'form_params' => $params]);

        $response = $this->client->post('https://merchantapi.leopardscourier.com/api/trackBookedPacket/format/json', [
            'http_errors' => FALSE,
            'form_params' => $params
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;


        // Reading response data
        if (isset($response['error']) && $response['error'] == 0) {
            if (isset($response['packet_list'][0]['Tracking Detail']) && !empty($response['packet_list'][0]['Tracking Detail'])) {
                $details = array_filter($response['packet_list'][0]['Tracking Detail'], function($detail) {
                    return (!empty($detail['Activity_Date']));
                });

                usort($details, function ($a, $b) {
                    $a = new Carbon($a['Activity_Date']);
                    $b = new Carbon($b['Activity_Date']);
                    return $a->gt($b);
                });

                foreach ($details as $key => $detail) {

                    try {
                        $temp_shipment_status_date = Carbon::parse($detail['Activity_datetime']);
                    } catch (Exception $e) {
                        $temp_shipment_status_date = Carbon::parse($detail['Activity_Date']);
                    }

                    if ($detail) {

                        $error = 0;
                        $message = 'Last Courier Status : '.$detail['Status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, ( isset($detail['Reciever_Name']) ? 'Receiver Name : '.$detail['Reciever_Name'].' | ' : ' ').( isset($detail['Reason']) ? $detail['Reason'] : '-'), $temp_shipment_status_date, $detail['Status'] ) );
                    } else {

                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }

                // to store booked and actual weight received from courier
                if ( isset($response['packet_list'][0]['booked_packet_weight']) && isset($response['packet_list'][0]['arival_dispatch_weight']) ){
                    // ShipmentWeightDescripancy::add($this->shipment->seller_id,$this->shipment->id,$this->shipment->weight,$response['packet_list'][0]['booked_packet_weight'],$response['packet_list'][0]['arival_dispatch_weight']);
                    ShipmentWeightDescripancy::updateOrCreate(['seller_id' => $this->shipment->seller_id, 'shipment_id' =>  $this->shipment->id], ['seller_id' =>  $this->shipment->seller_id , 'shipment_id' =>  $this->shipment->id , 'original_weight' =>  $this->shipment->weight , 'courier_booked_weight' =>  $response['packet_list'][0]['booked_packet_weight'] , 'courier_arival_weight' =>  $response['packet_list'][0]['arival_dispatch_weight'] ]);
                }


            } elseif (isset($response['packet_list'][0]['booked_packet_status'])) {

                $error = 0;
                $message = 'Tracking Details not found against this shipment #'.$this->tracking_number.', shipment overall status is used | '.$response['packet_list'][0]['booked_packet_status'];
                array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), $response['packet_list'][0]['booked_packet_status'] ) );
                
            } else {
                $message = 'Tracking Details Not Found and error not found | '.json_encode($response);
            }
            $this->overall_status = isset($response['packet_list'][0]['booked_packet_status']) ? $response['packet_list'][0]['booked_packet_status'] : null;

        } else if(isset($response['error'])) {
            
            if ($response['error'] === 'Invalid API Key/Password' && ($this->shipment->created_at->diffInDays(Carbon::now())) > 120) {
                
                // $error = 0;
                // $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days.';
                // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED'] ) );
                // array_push($this->comment_array , ['message' => $message, 'status' => 'Failed']);
                // array_push($this->exception_array , ['message' => $message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['INVALID-SHIPMENT-120']]);
                $message .= 'Got Exception Error On Tracking Request : Invalid API Key/Password';
                $error = 1;
            } else {
                $message = $response['error'];
            }
        } else {
            $message = 'Tracking Details Not Found | '.json_encode($response);
        }

        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        Log::info("LCS Merchant Sveston completed <".$this->tracking_number.">");

        return true;
    }


    // Calling Call Courier API to get response and reading response   ----------4TH CALL---------
    private function callCourier()
    {
        $url = 'http://cod.callcourier.com.pk/api/CallCourier/GetTackingHistory?cn='.$this->tracking_number;
        $this->request_dump = $url;
        $this->get_url = $url;
        $this->courier_name = 'Call Courier';
        $this->log_name = 'Call Courier '.$this->log_name;
        $message = '';

        $response = $this->client->get($url); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if ($response && is_array($response)) {

            foreach ($response as $key => $detail) {

                if ($detail) {
                    $error = 0;
                    $temp_shipment_status_date = explode('T',$detail['TransactionDate']);
                    $temp_shipment_status_date = Carbon::parse($temp_shipment_status_date[0].' '.$temp_shipment_status_date[1]);
                    $message = 'Last Courier Status : '.$detail['ProcessDescForPortal'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['OperationDesc'], $temp_shipment_status_date, $detail['ProcessDescForPortal'] ) );
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else if($response === 'No Record Found' && ($this->shipment->created_at->diffInDays(Carbon::now())) > 120) {
            // $error = 0;
            // $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days.';
            // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED'] ) );
            // array_push($this->comment_array , ['message' => $message, 'status' => 'Failed']);
            // array_push($this->exception_array , ['message' => $message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['INVALID-SHIPMENT-120']]);
            $message .= 'Got Exception Error On Tracking Request : '.$response;
            $error = 1;
        } else {
            $message = is_string($response) ? $response : json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling TRAX API to get response and reading response   ----------4TH CALL---------
    private function trax()
    {
        $url = 'https://sonic.pk/api/shipment/track/public?tracking_number='.$this->tracking_number;
        $this->request_dump = $url;
        $this->get_url = $url;
        $this->courier_name = 'Trax';
        $this->log_name = 'Trax '.$this->log_name;
        $message = '';

        $response = $this->client->get('https://sonic.pk/api/shipment/track/public?tracking_number='.$this->tracking_number.'&type=0'); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if ($response['status'] == 0) {

            foreach (array_reverse($response['details']['tracking_history']) as $key => $detail) {

                if ($detail) {
                    $error = 0;
                    $temp_shipment_status_date = Carbon::parse($detail['timestamp'])->setTimezone('Asia/Karachi');
                    $message = 'Last Courier Status : '.$detail['status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['status_reason'], $temp_shipment_status_date, $detail['status'] ) );
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else if($response['status'] == 1 && ($this->shipment->created_at->diffInDays(Carbon::now())) > 120) {
            // $error = 0;
            // $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days.';
            // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED'] ) );
            // array_push($this->comment_array , ['message' => $message, 'status' => 'Failed']);
            // array_push($this->exception_array , ['message' => $message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['INVALID-SHIPMENT-120']]);
            $message .= 'Got Exception Error On Tracking Request : ';
            $error = 1;

        } else {
            $message = json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling Blue Ex API to get response and reading response   ----------4TH CALL---------
    private function blueEx()
    {
        $this->courier_name = 'Blue Ex';
        $this->log_name = 'Blue Ex '.$this->log_name;
        $this->courier_status_matching_by = 'stringMatch';
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $account_no = DB::connection('mysql2')->table('admin_settings')->where('key','blue_ex_account_no')->value('value');
            $user_id = DB::connection('mysql2')->table('admin_settings')->where('key','blue_ex_user_id')->value('value');
            $password = DB::connection('mysql2')->table('admin_settings')->where('key','blue_ex_password')->value('value');
            $api_key = DB::connection('mysql2')->table('admin_settings')->where('key','blue_ex_api_key')->value('value');

            if ($account_no && $user_id && $password && $api_key) {
                $blueEx = new SellerCourierBlueEx;
                $blueEx->account_no = $account_no;
                $blueEx->user_id = $user_id;
                $blueEx->password = $password;
                $blueEx->api_key = $api_key;
            } else {
                $this->message = $this->log_name.' | BlueEx Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $blueEx = SellerCourierBlueEx::where('seller_id', $this->shipment->seller_id)->first();
        }

        \Log::info("BlueEx  <".$this->tracking_number.">  creds ready");

        if (empty($blueEx)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        \Log::info("BlueEx <".$this->tracking_number."> creds verified");

        // $data = json_encode([
        //     "acno"=> $blueEx->account_no,
        //     "userid"=> $blueEx->user_id,
        //     "password"=> $blueEx->password,
        //     "consignment_no"=> $this->tracking_number]);
            
        $url = 'https://bigazure.com/api/json_v3/tracking/tracking_detail.php';
        
        $this->request_dump = $url;
        $this->get_url = $url;

        // $response = $this->client->get($url);   // Request Send
        // $response = $response->getBody()->getContents(); // Request Response

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "consignment_no": "'.$this->tracking_number.'"
        }',
        CURLOPT_HTTPHEADER => array(
            'Authorization: Basic '.base64_encode($blueEx->account_no.':'.$blueEx->api_key).'',
            'Content-Type: application/json'
        ),
        ));

        \Log::info("BlueEx  <".$this->tracking_number.">  so far good");

        $response = curl_exec($curl);
        \Log::info($response);

        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        $proper_status = [
            "1" => "Booked",
            "2" => "Received",
            "3" => "In Transit",
            "4" => "Out For Delivery",
            "5" => "Delivered",
            "6" => "Returned",
            "7" => "Returned"
        ];

        // Reading response data
        if (isset($response['status']) && $response['status'] == 1 && isset($response['response']) && count($response['response']) > 0) {

            foreach (array_reverse($response['response']) as $key => $detail) {

                if ($detail) {
                    if (isset($detail['status_date']) && isset($detail['status_time'])) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::parse($detail['status_date'].' '.$detail['status_time']);
                        $message = 'Last Courier Status : '.$proper_status[$detail['status']].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $temp_shipment_status_date, $proper_status[$detail['status']] ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.( isset($detail['status']) ? $proper_status[$detail['status']] : 'Tracking detail not found');
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                    
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {

            if ($response['response']) {
                $message = json_encode($response['response']);
            } else {
                $message = 'Response message parameter is null';
            }
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling RIDER API to get response and reading response   ----------4TH CALL---------
    private function rider()
    {
        $this->courier_name = 'Rider';
        $this->log_name = 'Rider '.$this->log_name;

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $login_id = DB::connection('mysql2')->table('admin_settings')->where('key','rider_login_id')->value('value');
            $api_key = DB::connection('mysql2')->table('admin_settings')->where('key','rider_api_key')->value('value');

            if ($login_id && $api_key) {
                $rider = new SellerCourierRider;
                $rider->login_id = $login_id;
                $rider->api_key = $api_key;
            } else {
                $this->message = $this->log_name.' | Rider Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $rider = SellerCourierRider::where('seller_id', $this->shipment->seller_id)->first();
        }
        
        if (empty($rider)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $url = 'http://api.withrider.com/rider/v1/GetTackingHistory?cn='.$this->tracking_number.'&loginId='.$rider->login_id.'&apikey='.$rider->api_key;
        $this->get_url = $url;
        $this->request_dump = $url;
        $message = '';
        $local_tracking_array = [];
        $error = $this->error;

        try {

            $request = $this->client->get($url); // Request Send
            $response = $request->getBody()->getContents(); // Request Response
            $this->response_dump = $response;
            $response = json_decode($response, TRUE);


            // Reading response data
            if ($response) {

                foreach ($response as $key => $detail) {

                    if (isset($detail['order_status'])) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::parse($detail['updated_at']);
                        $message = 'Last Courier Status : '.$detail['order_status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['reason'], $temp_shipment_status_date, $detail['order_status'] ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }
            } else {
                $message = json_encode($response);
            }

        } catch (RequestException $e) {

            $error_response = json_decode(((string) $e->getResponse()->getBody()), true);
                
            if ( isset($error_response['message']) ) {
                $error = $error_response['message'];
            } elseif ( isset($error_response['errors']) ) {
                $error = json_encode($error_response['errors']);
            } else {
                $error = $e->getMessage();
            }

            if ($error == "Consignment id not found / Tracking history not available" && ($this->shipment->created_at->diffInDays(Carbon::now())) > 120) {
                // $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' is Cancelled because tracking details not found in response and it is older than 120 days.';
                // $error = 0;
                // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $message, Carbon::now(), 'Unity-Retail-'.config('enum.shipment_status')['CANCELLED'] ) );
                // array_push($this->comment_array , ['message' => $message, 'status' => 'Failed']);
                // array_push($this->exception_array , ['message' => $message, 'dump' => null, 'exception_type' => config('unityexception.exception_types')['INVALID-SHIPMENT-120']]);
                $message .= 'Got Exception Error On Tracking Request : '.$error;
                $error = 1;
                array_push($this->logging_array , ['message' => $message, 'skip_trace' => '']);
            } else {
                $message .= 'Got Request Exception Error On Tracking Request : '.$error;
                $error = 1;
                array_push($this->logging_array , ['message' => $message, 'skip_trace' => $e->getTraceAsString()]);
            }
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling Delivery Express to get response and reading response   ----------4TH CALL---------
    private function deliveryExpress()
    {
        $this->courier_name = 'TM Delivery Express';
        $this->log_name = 'TM Delivery Express '.$this->log_name;
        $message = '';
        $url = 'https://delex.pk/Web/tracking/'.$this->tracking_number;
        $this->request_dump = $url;
        $this->get_url = $url;


        $response = $this->client->get($url);   // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['Error']) && $response['Error'] == 0) {

            if (!strtotime($response['Last_Activity_Date'])) {
                $temp_shipment_status_date = $response['Order_Date'];
            } else {
                $temp_shipment_status_date = $response['Last_Activity_Date'];
            }

            
            if (isset($response['Order_Status'])) {
                
                if ($temp_shipment_status_date == '0000-00-00 00:00:00') {
                    $temp_shipment_status_date = Carbon::now();
                    $message = 'Last Courier Status : '.$response['Order_Status'].' | Last Status Tracking Read Time (Current Time) : '.$temp_shipment_status_date->toDateTimeString();
                } else {
                    $temp_shipment_status_date = Carbon::parse($temp_shipment_status_date);
                    $message = 'Last Courier Status : '.$response['Order_Status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                }

                $error = 0;
                array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $temp_shipment_status_date, $response['Order_Status'] ) );
            } else {
                $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found | '.json_encode($response);
                array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
            }

        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling TCS API to get response and reading response   ----------4TH CALL---------
    private function tcs()
    {
        $this->courier_name = 'TCS';
        $this->log_name = 'TCS '.$this->log_name;
        $message = '';
        
        $url = "https://apis.tcscourier.com/production/track/v1/shipments/detail?consignmentNo=".$this->tracking_number;
        $headers = [ 'x-ibm-client-id' => env('TCS_NEW_CLIENT_ID'), 'content-type' => 'application/json' ];
        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers]);

        $response = $this->client->get($url, ['headers'=>$headers]); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['returnStatus']['status']) && $response['returnStatus']['status'] == 'SUCCESS') {

            $temp_detail_array = [];
            $temp_detail_array = array_merge(   (isset($response['TrackDetailReply']['Checkpoints']) ? array_reverse($response['TrackDetailReply']['Checkpoints']) : []),
                                                (isset($response['TrackDetailReply']['DeliveryInfo']) ? array_reverse($response['TrackDetailReply']['DeliveryInfo']) : [])
            );

            if (empty($temp_detail_array)) {
                $message = 'Tracking details not found | '.json_encode($response);
            } else {
                foreach ($temp_detail_array as $key => $detail) {
    
                    if ($detail) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::parse($detail['dateTime']);
                        $message = 'Last Courier Status : '.$detail['status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, 'Package is at <b>'.(isset($detail['station']) ? $detail['station'].'</b> station'. (isset($detail['recievedBy']) ? ' | Package is received by <b>'.$detail['recievedBy'].'</b>' : '')  : $detail['recievedBy'].'</br>'), $temp_shipment_status_date, $detail['status'] ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }
            }
            
        } else {
            $message = json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling LCS-UAE API to get response and reading response   ----------4TH CALL---------
    private function lcsUAE()
    {
        $this->courier_name = 'LCS UAE';
        $this->log_name = 'LCS UAE '.$this->log_name;
        $message = '';
        
        $lcs_uae = SellerCourierLCSUAE::where('seller_id', $this->shipment->seller_id)->first();
        
        if (empty($lcs_uae)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $url = "http://courier.leopardsexpress.com/webservice/soapservice.php?wsdl";
        $in[] = ["BookingNumber" => $this->tracking_number,"CompanyCode"=>$lcs_uae->account_number];

        $this->request_dump = json_encode(['url' => $url, 'GetStatusDetails' => $in]);

        $client = new \SoapClient($url);
        $response= $client->__soapCall("GetStatusDetails",$in);
        $this->response_dump = $response;

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if ($response->responseCode == 1 && $response->responseArray) {

            foreach (array_reverse($response->responseArray) as $key => $detail) {

                if ($detail) {
                    $error = 0;
                    $temp_shipment_status_date = Carbon::createFromFormat('d/m/Y g:i A',$detail->SDate.' '.$detail->STime);
                    $message = 'Last Courier Status : '.$detail->CStatus.' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, 'Location : '.$detail->SLocation.' | Details : '.$detail->SDetails, $temp_shipment_status_date, $detail->CStatus ) );
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } elseif (isset($response->responseMessage)) {
            $message = $response->responseMessage;
        } else {
            $message = json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling SWYFT API to get response and reading response   ----------4TH CALL---------
    private function swyft()
    {
        $this->courier_name = 'Swyft';
        $this->log_name = 'Swyft '.$this->log_name;
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $vendor_id = DB::connection('mysql2')->table('admin_settings')->where('key','swyft_vendor_id')->value('value');
            $vendor_secret = DB::connection('mysql2')->table('admin_settings')->where('key','swyft_vendor_secret')->value('value');

            if ($vendor_id && $vendor_secret) {
                $swyft = new SellerCourierSwyft;
                $swyft->vendor_id = $vendor_id;
                $swyft->vendor_secret = $vendor_secret;
            } else {
                $this->message = $this->log_name.' | Swyft Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {    
            $swyft = SellerCourierSwyft::where('seller_id', $this->shipment->seller_id)->first();
        }

        if (empty($swyft)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }
        
        $url = env('SWYFT_API').$swyft->vendor_id.'/get-parcel-histroy/'.$this->tracking_number;
        $headers = [ 'Authorization' => $swyft->vendor_secret ];
        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers]);

        $response = $this->client->get($url, ['headers'=>$headers]); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response[0]['status']) && is_array($response)) {

            foreach ($response as $key => $detail) {

                $shipment_status_date = explode('T',$detail['date']);
                $shipment_status_date = Carbon::parse($shipment_status_date[0].' '.$shipment_status_date[1]);

                if ($detail) {
                    $error = 0;
                    $message = 'Last Courier Status : '.$detail['status'].' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $shipment_status_date, $detail['status'] ) );
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling DPD API to get response and reading response   ----------4TH CALL---------
    private function dpd()
    {
        $this->courier_name = 'DPD';
        $this->log_name = 'DPD '.$this->log_name;
        $message = '';

        $dpd = SellerCourierDPD::where('seller_id', $this->shipment->seller_id)->first();

        if (empty($dpd)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }
        
        $url = "https://apps.geopostuk.com/trackingcore/dpd/parcels";
        $headers = [ "Content-Type" => "text/xml",
                    "Accept" => "application/json",
                    "Cookie" => "ak_bmsc=EBF1C0AB041D1C1F97BAA7D220B686620214FC3EBF750000B082315F08A22A00~plZ9b4dTiFQO1oQVeKBJtUF1JnEkIH5fx8/d+ViHwBOCMc+HaeCccp9SU4RSluTWslw1l71PyXMhqLdo4NWup+9L/lsTx8J9szJuY33GXs1uqm/w5QG01LXrN/OI21QI1bjvPQmm4fatJVoYXPs1/RM0ARCtAc3nEvqdvKHo1F385ylZCXQbViBJYcSw6uPdDZWJjjBaPMzLzwrjmsHyCgQv5e8zK1Ot7B9n+4+NOHw+Q=; X-Mapping-mhdpbjif=250FAD21CC968A87638098272F020F60; bm_sv=E120DE647B33F1D2FD13757B161F2DC1C~8x6ADdgM5iWhxYn084njA4CvxmABzCZGCZyEPy31wIKIJM20xO0y1L+8zenSopnuk8qs2cFdHyEbSM6k40LX9vn959mBaFd3ClJ58TebCTEQktDaBWxJRPs/9ZIn8NTTba4PyhV97QuMHtCftDUEru+zjjeV13an/Y/jXE1s4cY=" ];
        
        $params = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<trackingrequest>\n <user>".$dpd->username."</user>\n <password>".$dpd->password."</password>\n <trackingnumbers>\n <trackingnumber>".$this->shipment->tracking_number."</trackingnumber>\n </trackingnumbers>\n</trackingrequest>";
        
        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers, 'form_params' => $params]);

        $response = $this->client->post($url, [
            'headers' => $headers,
            'body' => $params
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (count($response['trackingdetails']['trackingdetail']) > 5){
            $results = $response['trackingdetails']['trackingdetail']['trackingevents'];
        
            if (!isset($results['trackingevent']['type'])) {
                $results = array_reverse($results['trackingevent']);
            }

            foreach ($results as $key => $detail) {

                if ($detail) {
                    $error = 0;
                    $temp_shipment_status_date = Carbon::createFromFormat(\DateTime::ATOM,$detail['date']);
                    $message = 'Last Courier Status : '.$detail['type'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['description'].' <b> Locality : </b>'.$detail['locality'], $temp_shipment_status_date, $detail['type'] ) );
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
        }

      
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling DHL API to get response and reading response   ----------4TH CALL---------
    private function dhl()
    {
        $this->courier_name = 'DHL';
        $this->log_name = 'DHL '.$this->log_name;
        $message = '';

        $dhl = SellerCourierDHL::where('seller_id', $this->shipment->seller_id)->first();

        if (empty($dhl)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }
        
        $url = "https://wsbexpress.dhl.com:443/gbl/glDHLExpressTrack";
        $headers = [ "Content-Type " => "application/xml",
                    "Cookie" => "Cookie: BIGipServer~WSB~pl_wsb-express-cbj.dhl.com_443=310126791.64288.0000; BIGipServer~WSB~pl_wsb-express-chd.dhl.com_443=308824229.64288.0000" ];
        
        //$params = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:trac=\"http://scxgxtt.phx-dc.dhl.com/glDHLExpressTrack/providers/services/trackShipment\" xmlns:dhl=\"http://www.dhl.com\">\n<soapenv:Header>\n    <wsse:Security soapenv:mustUnderstand=\"1\" xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\">\n        <wsse:UsernameToken>\n            <wsse:Username>".$dhl->username."</wsse:Username>\n            <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">".$dhl->password."</wsse:Password>\n        </wsse:UsernameToken>\n    </wsse:Security>\n</soapenv:Header>\n   <soapenv:Body>\n      <trac:trackShipmentRequest>\n         <trackingRequest>\n            <dhl:TrackingRequest>\n               <!--You may enter the following 11 items in any order-->\n               <!--Optional:-->\n               <ClientDetail>\n                  <!--You may enter the following 2 items in any order-->\n                  <!--Optional:-->\n                  <sso>?</sso>\n                  <!--Optional:-->\n                  <plant>?</plant>\n               </ClientDetail>\n               <Request>\n\t\t            <ServiceHeader>\n\t\t               <!--You may enter the following 8 items in any order-->\n\t\t               <!--Optional:-->\n\t\t               <MessageTime>2020-08-19T08:45:56.967+00:00</MessageTime>\n\t\t               <MessageReference>1821c4eb5b7b41f4a38ea12292d90870</MessageReference>\n\t\t               <!--Optional:-->\n\t\t               <WebstorePlatform>Unity</WebstorePlatform>\n\t\t               <!--Optional:-->\n\t\t               <WebstorePlatformVersion>1.0</WebstorePlatformVersion>\n\t\t               <!--Optional:-->\n\t\t               <ShippingSystemPlatform>Unity</ShippingSystemPlatform>\n\t\t               <!--Optional:-->\n\t\t               <ShippingSystemPlatformVersion>1.0</ShippingSystemPlatformVersion>\n\t\t            </ServiceHeader>\n               </Request>\n               <AWBNumber>\n                  <!--1 to 100 repetitions:-->\n                  <ArrayOfAWBNumberItem>".$this->tracking_number."</ArrayOfAWBNumberItem>\n               </AWBNumber>\n               <LevelOfDetails>ALL_CHECKPOINTS</LevelOfDetails>\n               <!--Optional:-->\n               <PiecesEnabled>S</PiecesEnabled>\n            </dhl:TrackingRequest>\n         </trackingRequest>\n      </trac:trackShipmentRequest>\n   </soapenv:Body>\n</soapenv:Envelope>";
        
        $user_name = $dhl->username;
        $password = $dhl->password;
        $tracking_number =  $this->tracking_number;

        $params = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:trac="http://scxgxtt.phx-dc.dhl.com/glDHLExpressTrack/providers/services/trackShipment" xmlns:dhl="http://www.dhl.com">
                        <soapenv:Header>
                            <wsse:Security soapenv:mustUnderstand="1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                                <wsse:UsernameToken>
                                    <wsse:Username>'.$user_name.'</wsse:Username>
                                    <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">'.$password.'</wsse:Password>
                                </wsse:UsernameToken>
                            </wsse:Security>
                        </soapenv:Header>
                    <soapenv:Body>
                        <trac:trackShipmentRequest>
                            <trackingRequest>
                                <dhl:TrackingRequest>
                                <!--You may enter the following 11 items in any order-->
                                <!--Optional:-->
                                <ClientDetail>
                                    <!--You may enter the following 2 items in any order-->
                                    <!--Optional:-->
                                    <sso>?</sso>
                                    <!--Optional:-->
                                    <plant>?</plant>
                                </ClientDetail>
                                <Request>
                                        <ServiceHeader>
                                        <!--You may enter the following 8 items in any order-->
                                        <!--Optional:-->
                                        <MessageTime>2020-08-19T08:45:56.967+00:00</MessageTime>
                                        <MessageReference>1821c4eb5b7b41f4a38ea12292d90870</MessageReference>
                                        <!--Optional:-->
                                        <WebstorePlatform>Unity</WebstorePlatform>
                                        <!--Optional:-->
                                        <WebstorePlatformVersion>1.0</WebstorePlatformVersion>
                                        <!--Optional:-->
                                        <ShippingSystemPlatform>Unity</ShippingSystemPlatform>
                                        <!--Optional:-->
                                        <ShippingSystemPlatformVersion>1.0</ShippingSystemPlatformVersion>
                                        </ServiceHeader>
                                </Request>
                                <AWBNumber>
                                    <!--1 to 100 repetitions:-->
                                    <ArrayOfAWBNumberItem>'.$tracking_number.'</ArrayOfAWBNumberItem>
                                </AWBNumber>
                                <LevelOfDetails>ALL_CHECKPOINTS</LevelOfDetails>
                                <!--Optional:-->
                                <PiecesEnabled>S</PiecesEnabled>
                                </dhl:TrackingRequest>
                            </trackingRequest>
                        </trac:trackShipmentRequest>
                    </soapenv:Body>
                    </soapenv:Envelope>';
 
        $curl = curl_init();
    
 
        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_RETURNTRANSFER => TRUE,
        CURLOPT_ENCODING       => 'UTF-8',
        CURLOPT_POSTFIELDS =>$params,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/xml',
            'Cookie: BIGipServer~WSB~pl_wsb-express-cbj.dhl.com_443=310126791.64288.0000; BIGipServer~WSB~pl_wsb-express-chd.dhl.com_443=308824229.64288.0000'
        ),
        ));
        
        $response = curl_exec($curl);

        $this->request_dump = json_encode(['url' => $url, 'form_params' => $params]);
        $this->response_dump = $response;

        $response = str_ireplace(['<?xml version="1.0" encoding="UTF-8"?>', ''], '', $response);
        $clean_xml = str_ireplace(['SOAP-ENV:', 'SOAP:'], '', $response);
        $cleaner_xml = str_ireplace(['ser-root:', 'SOAP:'], '', $clean_xml);
        $neat_xml = str_ireplace(['ns:', 'SOAP:'], '', $cleaner_xml);
        $xml = simplexml_load_string($neat_xml);
        $json = json_encode($xml);
        $responseArray = json_decode($json,true);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($responseArray['Body']['trackShipmentRequestResponse']['trackingResponse']['TrackingResponse']['AWBInfo']['ArrayOfAWBInfoItem']['ShipmentInfo'])) {
            $shipment_info = $responseArray['Body']['trackShipmentRequestResponse']['trackingResponse']['TrackingResponse']['AWBInfo']['ArrayOfAWBInfoItem']['ShipmentInfo'];
        
            if(array_key_exists('ShipmentEvent',$shipment_info))
            {
                foreach($shipment_info['ShipmentEvent']['ArrayOfShipmentEventItem'] as $key => $detail){
    
                    if ($detail) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::parse($detail['Date'].' '.$detail['Time']);
                        $message = 'Last Courier Status : '.$detail['ServiceEvent']['EventCode'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['ServiceEvent']['Description'].' ('.$detail['ServiceEvent']['EventCode'].') | Area : '.$detail['ServiceArea']['Description'].' ('.$detail['ServiceArea']['ServiceAreaCode'].')', $temp_shipment_status_date, $detail['ServiceEvent']['EventCode'] ) );
                    } else {
                        $message = 'Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }
            }  else {
                $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Shipment Event Not Exists, because may be its a newly created shipment';
            }

        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling CourierX API to get response and reading response   ----------4TH CALL---------
    private function courierx()
    {
        $this->courier_name = 'CourierX';
        $this->log_name = 'CourierX '.$this->log_name;
        $message = '';

       
        $courierx = SellerCourierCourierX::where('seller_id', $this->shipment->seller_id)->first();
        


        if (empty($courierx)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }
        
        $url = env('COURIERX_URL').'/api/PortalAPI/GetCNTracking?CN='.$this->shipment->tracking_number;
    
        $this->request_dump = json_encode(['url' => $url]);

        $response = $this->client->get($url); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        $st = '';
     

        // Reading response data
        if (isset($response['Details']) && is_array($response['Details'])) {

            foreach ($response['Details'] as $key => $detail) {
                Log::info($key);
                if($key % 2 == 0){
                    $st = '';
                    $st = explode(':',$detail)[1];
                    Log::info($st);
                } else{
                    // $shipment_status_date = explode('T',$detail['date']);
                    $exp = explode(':',$detail);
                    
                    $d = $exp[1].':'.$exp[2].':'.$exp[3];
                    Log::info($d);
                    $shipment_status_date = Carbon::createFromFormat('Y/m/d H:i:s',$d);
                    Log::info($shipment_status_date);
                    if ($st != '') {
                        $error = 0;
                        $message = 'Last Courier Status : '.$st.' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $st, $shipment_status_date, $st ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }
               
            }

        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling MoveX API to get response and reading response   ----------4TH CALL---------
    private function movex()
    {
        $this->courier_name = 'MoveX';
        $this->log_name = 'MoveX '.$this->log_name;
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $api_key = DB::connection('mysql2')->table('admin_settings')->where('key','movex_api_key')->value('value');

            if ($api_key) {
                $movex = new SellerCourierMoveX;
                $movex->api_key = $api_key;
            } else {
                $this->message = $this->log_name.' | MoveX Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $movex = SellerCourierMoveX::where('seller_id', $this->shipment->seller_id)->first();
        }


        if (empty($movex)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }
        
        $url = 'https://tracking.movexpk.com/api/track/consignments?consignment_numbers='.$this->tracking_number;
        $headers = [ 'Authorization' => $movex->api_key ];
        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers]);

        $response = $this->client->get($url, ['headers'=>$headers]); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['response']['success'][0]['tracking_details']['tracking_info']) && is_array($response['response']['success'][0]['tracking_details']['tracking_info'])) {

            foreach (array_reverse($response['response']['success'][0]['tracking_details']['tracking_info']) as $key => $detail) {

                // $shipment_status_date = explode('T',$detail['date']);
                $shipment_status_date = Carbon::parse($detail['created_datetime']);

                if ($detail) {
                    $error = 0;
                    $message = 'Last Courier Status : '.$detail['order_status'].' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['message'], $shipment_status_date, $detail['order_status'] ) );
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling CARSON API to get response and reading response   ----------4TH CALL---------
    private function carson()
    {
        $this->courier_name = 'Carson';
        $this->log_name = 'Carson '.$this->log_name;
        $message = '';

        $carson = SellerCourierCarson::where('seller_id', $this->shipment->seller_id)->first(['email','password']);

        if (empty($carson)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }
        
        $url = env('CARSON_TRACKING_URL');
        $headers = [ "Content-Type" => "application/json"];
        $params = [
            "password" => $carson->password,
            "email" => $carson->email,
            "awb" => [ [ $this->tracking_number ] ]
        ];

        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers, 'form_params' => $params]);

        $response = $this->client->post($url, [
            'headers' => $headers,
            'body' => json_encode($params)
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['data'][0]['status'])) {

            foreach ($response['data'] as $key => $detail) {

                if ($detail) {
                    if($this->shipmentCourierHistoryPreviousStatus($detail['status'])){
                        $this->exception_enabled = false;
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Carsons Same Status | Status: '.$detail['status'];
                    } else {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::now();
                        $message = 'Last Courier Status : '.$detail['status'].' | Last Status Tracking Read Time (Current Time) : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $temp_shipment_status_date, $detail['status'] ) );
                    }
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }
        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
        }

        //Below is the temporary code to mark as delivered forcefully
        // Log::info('Carson Log Started |'.$this->tracking_number.' Delivered list');
        // $local_tracking_array = [];
        // $error = $this->error;
        // $del_tracking_numbers_list = 0;
        // $temp_array = ['1000341537','1000341553','1000341563','1000341567','1000341577','1000341819','1000341826','1000341832','1000342079','1000355270','1000359553','1000361084','1000369234','1000369656','1000372046'];
        // foreach($temp_array as $temp){
        //     if($this->tracking_number == $temp){
        //      $del_tracking_numbers_list=1;
        //     }
        //  }

        // if($del_tracking_numbers_list == 1){
        //     $shipment_status = "DELIVERED";
        //     Log::info('Carson Tracking Number |'.$this->tracking_number.' Delivered list');
        // }
        // $shipment_status_date = Carbon::now();
        // Log::info('Carson Tracking Number |'.$this->tracking_number.'--'.$shipment_status_date.'--'.$shipment_status_date->toDateTimeString().'--Delivered list');
        // $error = 0;
        // $message = 'Delivered Courier Status : '.$shipment_status.' | Last Status Time : '.$shipment_status_date->toDateTimeString();
        // array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, 'Delivered | null', $shipment_status_date, $shipment_status ) );


        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling DELYBELL API to get response and reading response   ----------4TH CALL---------
    private function delybell()
    {
        $this->courier_name = 'Delybell';
        $this->log_name = 'Delybell '.$this->log_name;
        $message = '';

        $delybell = SellerCourierDelybell::where('seller_id', $this->shipment->seller_id)->first();

        if (empty($delybell)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }
        
        $url = 'https://www.delybell.com/api/home/<USER>';
        $headers = [ "Content-Type" => "application/json"];
        
        $params = [
            "api_token" => $delybell->api_token,
            "orderId" => $this->tracking_number
        ];

        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers, 'body' => $params]);

        $response = $this->client->post($url, [
            'headers' => $headers,
            'body' => json_encode($params)
        ]); // Request Send

        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;
        // Reading response data
        if ($response['error'] == false) {

            if (isset($response['DeliveryStatus'])) {
                if($this->shipmentCourierHistoryPreviousStatus($response['DeliveryStatus'])){
                    $this->exception_enabled = false;
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Delybell Same Status | Status: '.$response['DeliveryStatus'];
                } else {
                    $error = 0;
                    if(in_array($response['DeliveryStatus'], config('courierstatuses.delybell')['DELIVERED'])) {
                        $temp_shipment_status_date = Carbon::parse($response['deliveryDetails']['orderCompletedDate']);
                        $message = 'Last Courier Status : '.$response['DeliveryStatus'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                    } else {
                        $temp_shipment_status_date = Carbon::now();
                        $message = 'Last Courier Status : '.$response['DeliveryStatus'].' | Last Status Tracking Read Time (Current Time) : '.$temp_shipment_status_date->toDateTimeString();
                    }

                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, 'Order Delivered to '.$response['deliveryDetails']['deliveryName'].' in city '.$response['deliveryDetails']['deliveryCity'], $temp_shipment_status_date, $response['DeliveryStatus'] ) );
                }
            } else {
                $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Delivery Status Parameter not found';
                array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
            }
            
        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling DELYBELL API to get response and reading response   ----------4TH CALL---------
    private function quiqup()
    {
        $this->courier_name = 'Quiqup';
        $this->log_name = 'Quiqup '.$this->log_name;
        $message = '';

        $quiqup = SellerCourierQuiqup::where('seller_id', $this->shipment->seller_id)->first();

        if (empty($quiqup)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $params = [
            "grant_type" => "client_credentials",
            "client_id" => $quiqup->app_key,
            "client_secret" => $quiqup->app_secret,
        ];

        // dd(json_encode($params));

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('QUIQUP_API').'/oauth/token',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($params),
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $token_response = curl_exec($curl);
        $token_response = json_decode($token_response, TRUE);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        if($httpCode == '200' || $httpCode == '201')
        {
            $url = env('QUIQUP_API').'/orders/'.$this->tracking_number;
        
        
            $headers = [ "Authorization" => "Bearer ".$token_response['access_token']];
    
            $this->request_dump = json_encode(['url' => $url,'headers' => $headers]);
    
            $response = $this->client->get($url,[
                'headers' => $headers,
            ]); // Request Send
    
            $response = $response->getBody()->getContents(); // Request Response
            $this->response_dump = $response;
            $response = json_decode($response, TRUE);
    
            $local_tracking_array = [];
            $error = $this->error;
            // Reading response data
            if (!isset($response['error'])) {
    
                if (isset($response['order'])) {
                    
    
                        
                    $shipment_status = $response['order']['state'];
                    
    
                    $shipment_status_date = Carbon::parse($response['order']['state_updated_at'],'GMT+4')->setTimezone(config('app.timezone'));
    
                    
                    $error = 0;
                    $message = 'Last Courier Status : '.$shipment_status.' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $shipment_status_date, $shipment_status ) );
                    
                    
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Delivery Status Parameter not found';
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
                
            } else {
                $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
            }
        } else{
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Access Token Issue | '.(isset($token_response['error']) ? $token_response['error'] : json_encode($token_response));
        }
            
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;

        
        
        
    }

     // Calling MoveX API to get response and reading response   ----------4TH CALL---------
     private function amazonShipping()
    {
        $this->courier_name = 'Amazon-Shipping';
        $this->log_name = 'Amazon-Shipping '.$this->log_name;
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $api_key = DB::connection('mysql2')->table('admin_settings')->where('key','amazon_shipping_api_key')->value('value');

            if ($api_key) {
                $amazon_shipping = new SellerCourierAmazonShipping();
                $amazon_shipping->api_key = $api_key;
            } else {
                $this->message = $this->log_name.' | Amazon Shipping Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $amazon_shipping = SellerCourierAmazonShipping::where('seller_id', $this->shipment->seller_id)->first();
        }


        if (empty($amazon_shipping)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $url = 'https://api.trackingmore.com/v3/trackings/realtime';
        $headers = [
            "Content-Type" => "application/json",
            "Tracking-Api-Key" => $amazon_shipping->api_key,
        ];
        $params = [
            "tracking_number" => $this->tracking_number,
            "courier_code" => 'amazon-uk',
        ];

        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers, 'form_params' => $params]);

        $response = $this->client->post($url, [
            'headers' => $headers,
            'body' => json_encode($params)
        ]); // Request Send
        
        // $url = 'https://api.trackingmore.com/v3/trackings/realtime'.$this->tracking_number;
        // $headers = [ 'Authorization' => $movex->api_key ];
        // $this->request_dump = json_encode(['url' => $url, 'headers' => $headers]);

        // $response = $this->client->get($url, ['headers'=>$headers]); 

        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.'| The delivery status is '.$response['data']['delivery_status']);
        // Reading response data
        if (isset($response['data']['origin_info']['trackinfo']) && is_array($response['data']['origin_info']['trackinfo']) && !empty($response['data']['origin_info']['trackinfo']) ) {

            foreach (array_reverse($response['data']['origin_info']['trackinfo']) as $key => $detail) {

                if($detail['checkpoint_delivery_status'] == 'pending')
                    $shipment_status = $detail['checkpoint_delivery_status'];
                else{
                    $shipment_status = $detail['checkpoint_delivery_substatus'];
                }

                if($detail['tracking_detail'] == 'swa_rex_detail_package_picked_by_customer'){
                    $shipment_status = "DELIVERED";
                }

                $lost_tracking_numbers_list = 0;
                $temp_array = ['TF507090857GB','A11069036533','A11019576803','A11019576913','A11019576603','A11019577393','A11019577903','A11001388933','A11000110053','A10993908893','A10992941053','TF507057224GB','A11000101563','TF507059843GB','A11005725273','A11006730863','A11006737383','A11006805103','TF507066685GB','A11008190723','TF507072221GB','TF507074681GB','A11019576563','A11019577023','A11022448863','A11028975933','TF507089709GB','TF507091035GB','TF507102609GB','A11046594423','A11046942173','A11047456403','A11048125993','TF507113456GB','A11053089913','A11063139303'];
                foreach($temp_array as $temp){
                    if($this->tracking_number == $temp){
                     $lost_tracking_numbers_list=1;
                    }
                 }
        
                if($lost_tracking_numbers_list == 1){
                    $shipment_status = "FORCED_LOST";
                    Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.' Lost list');
                }
                $shipment_status_date = Carbon::parse($detail['checkpoint_date']);

                if ($detail) {
                    $error = 0;
                    $message = 'Last Courier Status : '.$shipment_status.' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['tracking_detail'].' | '.$detail['location'], $shipment_status_date, $shipment_status ) );
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            /*Temporary work till not found issues are resolved - to address 45 days older shipmentns - api shared by sherry*/
            /*Start*/
            if(isset($response['data']['delivery_status']) && ($response['data']['delivery_status'] == "notfound" || $response['data']['delivery_status'] == "expired")){
                Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.'| '.$response['data']['delivery_status'].' on Trackingmore');
                $response = $this->client->get('https://track.amazon.co.uk/api/tracker/'.$this->tracking_number);
                $response = $response->getBody()->getContents();
                $response = json_decode($response, TRUE);

                if ($response && isset($response['progressTracker'])) {
                    $response = json_decode($response['progressTracker'], TRUE);

                    if(isset($response['errors'][0]['errorCode']) && $response['errors'][0]['errorCode'] = "SHIPMENT_OLDER_THAN_SUPPORTED_AGE"){
                        $error = 0;
                        Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.'| Older thenn 45 days');
                        $shipment_status = $response['summary']['status'];
                        $lost_tracking_numbers_list = 0;
                        $temp_array = ['A11019577903','A11001388933','A11000110053','A10993908893'];
                        foreach($temp_array as $temp){
                           if($this->tracking_number == $temp){
                            $lost_tracking_numbers_list=1;
                           }
                        }
                        if(isset($response['summary']["metadata"]["deliveryDate"])){
                            $shipment_status_date= Carbon::parse($response['summary']["metadata"]["deliveryDate"]["date"]);
                        }else{
                            $shipment_status_date = Carbon::now();
                        }
    
                        if($lost_tracking_numbers_list == 1){
                            $shipment_status = "FORCED_LOST";
                            Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.' Lost list');
                        }
        
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0,$response['errors'][0]['errorMessage'] , $shipment_status_date, $shipment_status ) );
                        $message = 'Last Courier Status tracker : '.$shipment_status.' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                    }else{
                        Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.'| No error code found');
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
                    } 
                }else{
                    Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.'| No error code found');
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
                } 
                /*End*/            
            }else{
                Log::info('Amazon Shiping Tracking Number |'.$this->tracking_number.'| Tracking Info array not found on Trackingmore');
                $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
            }
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;
        
        return true;
    }

    // Calling Forrun API to get response and reading response   ----------4TH CALL---------
    private function forrun()
    {
        $this->courier_name = 'Forrun';
        $this->log_name = 'Forrun '.$this->log_name;
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $api_token = DB::connection('mysql2')->table('admin_settings')->where('key','forrun_api_token')->value('value');
            $account_id = DB::connection('mysql2')->table('admin_settings')->where('key','forrun_account_id')->value('value');

            if ($api_token && $account_id) {
                $forrun = new SellerCourierForrun;
                $forrun->api_token = $api_token;
                $forrun->account_id = $account_id;
            } else {
                $this->message = $this->log_name.' | Forrun | Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $forrun = SellerCourierForrun::where('seller_id', $this->shipment->seller_id)->first();
        }
      

        if (empty($forrun)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $url = 'https://'.env('FORRUN_API','forrun.co').'/api/v1/get_statuses';
        
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => 'account_id='.$forrun->account_id.'&api_token='.$forrun->api_token.'&tracking_id='.$this->tracking_number,
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded'
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response, TRUE);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

      
        $this->response_dump = $response;
       

        $local_tracking_array = [];
        $error = $this->error;
        // Reading response data
       
        Log::info('Forrun | '.$this->tracking_number.' | '.json_encode($response));
        if (isset($response['code']) && $response['code'] == 200) {
            
            foreach ($response['orders']['statuses'] as $key => $detail) {
                
                $shipment_status = $detail['name'];
                if($shipment_status == 'Shipper Advice'){
                    event(new ForrunShipperAdviceEvent($this->shipment->seller_id,$this->tracking_number,$shipment_status));
                }

                $shipment_status_date = Carbon::createFromFormat('d/m/Y h:i A',$detail['updated_at']);

                if($detail){
                    $error = 0;
                    $message = 'Last Courier Status : '.$shipment_status.' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $shipment_status_date, $shipment_status ) );
                }
                else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }
            
        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Forrun Message | '.$response['message'];
            array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
        }
            
       
        
            
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;

        
        
        
    }

    // Calling PostEx API to get response and reading response   ----------4TH CALL---------
    private function postex()
    {
        $this->courier_name = 'PostEx';
        $this->log_name = 'PostEx '.$this->log_name;
        $message = '';

        $postex = SellerCourierPostEx::where('seller_id', $this->shipment->seller_id)->first();

        if (empty($postex)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $postex_code_mapping = array(
            "0001" => "At Merchant’s Warehouse",
            "0002" => "Returned",
            "0003" => "At PostEx Warehouse",
            "0004"=> "Package on Root",
            "0005"=> "Delivered",
            "0006" => "Returned",
            "0007" => "Returned",
            "0008" => "Delivery Under Review",
            "0013" => "Attempt Made"
        );


        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('POSTEX_API').'/external-app/order-management/v2/track-order/'.$this->tracking_number,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
        'token: '.$postex->token,
        'authorization: '.$postex->authorization
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response,true);
        curl_close($curl);


        $this->response_dump = $response;

        $local_tracking_array = [];
        $error = $this->error;
        // Reading response data
        
        Log::info('PostEx | '.$this->tracking_number.' | '.json_encode($response));
        if(isset($response['statusCode']) && $response['statusCode'] == "200" && isset($response['dist']['transactionStatusHistory'])){
            foreach ($response['dist']['transactionStatusHistory'] as $key => $detail) {
                if($detail){
                    $error = 0;
                    $shipment_status_date = Carbon::parse($detail['updatedAt'])->setTimezone('Asia/Karachi');
                    $status_message = $detail['transactionStatusMessage'];
                    $status_code = $detail['transactionStatusMessageCode'];
                    $message = 'Post Ex - Last Courier Status : '.$status_code.' | Last Status Time : '.$shipment_status_date;
                    // $status_code = $detail['transactionStatusMessage'];

                    $message = 'Post Ex - Last Courier Status : '.$status_code.' | Last Status Time : '.$shipment_status_date;
                //    echo $shipment_status_date."--------".$status_message."----------".$status_code."<br>";
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $status_message, $shipment_status_date, $status_code ) );
                }else{
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }
        }else{
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | PostEx Message | '.$response['statusMessage'];
            array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
        }     
            
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
        
    }

    private function postex_partner()
    {
        $this->courier_name = 'PostEx-Partner';
        $this->log_name = 'PostEx Partner '.$this->log_name;
        $message = '';

        $postex = SellerCourierPostExPartner::where('seller_id', $this->shipment->seller_id)->first();

        if (empty($postex)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $postex_code_mapping = array(
            "0001" => "At Merchant’s Warehouse",
            "0002" => "Returned",
            "0003" => "At PostEx Warehouse",
            "0004"=> "Package on Root",
            "0005"=> "Delivered",
            "0006" => "Returned",
            "0007" => "Returned",
            "0008" => "Delivery Under Review",
            "0013" => "Attempt Made"
        );


        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => env('POSTEX_API').'/order/v1/track-order/'.$this->tracking_number,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
        'token: '.$postex->token,
        'authorization: '.$postex->authorization
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response,true);
        curl_close($curl);


        $this->response_dump = $response;

        $local_tracking_array = [];
        $error = $this->error;
        // Reading response data
        
        Log::info('PostEx | '.$this->tracking_number.' | '.json_encode($response));
        if(isset($response['statusCode']) && $response['statusCode'] == "200" && isset($response['dist']['transactionStatusHistory'])){
            foreach ($response['dist']['transactionStatusHistory'] as $key => $detail) {
                if($detail){
                    $error = 0;
                    $shipment_status_date = Carbon::parse($detail['updatedAt'])->setTimezone('Asia/Karachi');
                    $status_message = $detail['transactionStatusMessage'];
                    $status_code = $detail['transactionStatusMessageCode'];
                    $message = 'PostEx Partner - Last Courier Status : '.$status_code.' | Last Status Time : '.$shipment_status_date;
                    // $status_code = $detail['transactionStatusMessage'];

                    $message = 'PostEx Partner - Last Courier Status : '.$status_code.' | Last Status Time : '.$shipment_status_date;
                //    echo $shipment_status_date."--------".$status_message."----------".$status_code."<br>";
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $status_message, $shipment_status_date, $status_code ) );
                }else{
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }
        }else{
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | PostEx Partner Message | '.($response ? json_encode($response) : $response);
            array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
        }     
            
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
        
    }

    // Calling MoveX API to get response and reading response   ----------4TH CALL---------
    private function stallion()
    {
        $this->courier_name = 'Stallion';
        $this->log_name = 'Stallion '.$this->log_name;
        $message = '';

        $stallion = SellerCourierStallion::where('seller_id', $this->shipment->seller_id)->first();

        if (empty($stallion)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $response1 = $this->client->get(env('STALLION_URL').'oauth/v2/Token', [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'password',
                'username' => $stallion->username,
                'password' => $stallion->password,
            ],
        ]); 

        $local_tracking_array = [];
        $error = $this->error;

        $responses = $response1->getBody()->getContents(); 
        $responses = json_decode($responses, TRUE);
        $httpCode = $response1->getStatusCode();

        if($httpCode == 200 || $httpCode == 201)
        { 
            if(isset($responses['access_token'])){
                $url = env('STALLION_URL').'Stallion/TrackingAPI/';
                $headers = [ 'Authorization' => 'Bearer '.$responses['access_token'] ];
                $params = [ "CNNumber" => $this->tracking_number];
                $this->request_dump = json_encode(['url' => $url, 'headers' => $headers]);

                $response = $this->client->post($url, ['headers'=>$headers, 'form_params' => $params]); // Request Send
                $httpCode1 = $response->getStatusCode();
                $response = $response->getBody()->getContents(); // Request Response
                
                $this->response_dump = $response;
                $response = json_decode($response, TRUE);

                

                // Reading response data
                if (in_array($httpCode1,[200,201]) && isset($response['TrackingAPI']) && is_array($response['TrackingAPI']) && count($response['TrackingAPI']) > 0) {
                    foreach (array_reverse($response['TrackingAPI']) as $key => $detail) {

                        // $shipment_status_date = explode('T',$detail['date']);
                        $ts = explode('Date(',$detail['Extacttimestamp']);
                        $dt = explode(')',$ts[1]);
                        $shipment_status_date = Carbon::createFromTimestamp(intval((int)$dt[0]/1000));

                        if ($detail) {
                            $error = 0;
                            $message = 'Last Courier Status : '.$detail['StatusName'].' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                            array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '', $shipment_status_date, $detail['StatusName'] ) );
                        } else {
                            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                            array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                        }
                    }

                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.json_encode($response);
                }
                
                $this->message = $message;
                $this->tracking_array = $local_tracking_array;
                $this->error = $error;

                return true;
            } else{
                $this->message = $this->log_name.' | Token Api Error1 | '.json_encode($responses);
                return true;
            }
        } else{
            $this->message = $this->log_name.' | Token Api Error | '.json_encode($responses);
            return true;
        }

        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;

    }
    // Calling Time Express API to get response and reading response   ----------4TH CALL---------
    private function timeExpress()
    {
        $this->courier_name = 'Time-Express';
        $this->log_name = 'Time-Express '.$this->log_name;
        $message = '';

        
        $TimeExpress = SellerCourierTimeExpress::where('seller_id', $this->shipment->seller_id)->first();
      

        if (empty($TimeExpress)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

        $url = env('TIME_EXPRESS_URL').'Mobile/TimeServices.svc/trackdetailslist';
        
        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS =>'{
            "awbno":'.$this->tracking_number.'
        }',
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
        ));

        $response = curl_exec($curl);
        $response = json_decode($response, TRUE);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

      
        $this->response_dump = $response;
       

        $local_tracking_array = [];
        $error = $this->error;
        // Reading response data
       
        Log::info('Time Express | '.$this->tracking_number.' | '.json_encode($response));
        if (isset($response['code']) && isset($response['trackList']) && $response['code'] == 1 && count($response['trackList']) > 0) {
            
            foreach ($response['trackList'] as $key => $detail) {
                if($detail){
                    $shipment_status = $detail['StatusCode'];
                    $dt = explode(' ',$detail['TransDate']);
                    $shipment_status_date = Carbon::createFromFormat('m/d/Y H:i',$dt[0].' '.$detail['TransTime'],'GMT+4')->setTimezone(config('app.timezone'));
                    
                    $error = 0;
                    $message = 'Last Courier Status : '.$shipment_status.' | Last Status Time : '.$shipment_status_date->toDateTimeString();
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['Status'].' | Area: '.$detail['Location'], $shipment_status_date, $shipment_status ) );
                }
                else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }
            
        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Time Express Message | '.$response['description'];
            array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
        }
            
       
        
            
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;

        
        
        
    }
    // Calling TCS API to get response and reading response   ----------4TH CALL---------
    private function tcsuae()
    {
        $this->courier_name = 'TCSUAE';
        $this->log_name = 'TCSUAE '.$this->log_name;
        $message = '';
        
        $url = "https://apis.tcscourier.com/production/track/v1/shipments/detail?consignmentNo=".$this->tracking_number;
        $headers = [ 'x-ibm-client-id' => env('TCS_NEW_CLIENT_ID'), 'content-type' => 'application/json' ];
        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers]);

        $response = $this->client->get($url, ['headers'=>$headers]); // Request Send
        $response = $response->getBody()->getContents(); // Request Response
        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['returnStatus']['status']) && $response['returnStatus']['status'] == 'SUCCESS') {

            $temp_detail_array = [];
            $temp_detail_array = array_merge(   (isset($response['TrackDetailReply']['Checkpoints']) ? array_reverse($response['TrackDetailReply']['Checkpoints']) : []),
                                                (isset($response['TrackDetailReply']['DeliveryInfo']) ? array_reverse($response['TrackDetailReply']['DeliveryInfo']) : [])
            );

            if (empty($temp_detail_array)) {
                $message = 'Tracking details not found | '.json_encode($response);
            } else {
                foreach ($temp_detail_array as $key => $detail) {
    
                    if ($detail) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::parse($detail['dateTime'],'GMT+4')->setTimezone(config('app.timezone'));
                        // $temp_shipment_status_date = Carbon::parse($detail['dateTime']);
                        $message = 'Last Courier Status : '.$detail['status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, 'Package is at <b>'.(isset($detail['station']) ? $detail['station'].'</b> station'. (isset($detail['recievedBy']) ? ' | Package is received by <b>'.$detail['recievedBy'].'</b>' : '')  : $detail['recievedBy'].'</br>'), $temp_shipment_status_date, $detail['status'] ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                }
            }
            
        } else {
            $message = json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    // Calling Blue Ex API to get response and reading response   ----------4TH CALL---------
    private function daewoo()
    {
        $this->courier_name = 'Daewoo';
        $this->log_name = 'Daewoo '.$this->log_name;
        $message = '';

        if($this->shipment->shipment_booking_courier->value === "universal") {
            $user = DB::connection('mysql2')->table('admin_settings')->where('key','daewoo_user')->value('value');
            $password = DB::connection('mysql2')->table('admin_settings')->where('key','daewoo_password')->value('value');
            $api_key = DB::connection('mysql2')->table('admin_settings')->where('key','daewoo_api_key')->value('value');

            if ($user && $password && $api_key) {
                $daewoo = new SellerCourierDaewoo();
                $daewoo->user = $user;
                $daewoo->password = $password;
                $daewoo->api_key = $api_key;
            } else {
                $this->message = $this->log_name.' | Daewoo Universal Account Credentials not found , please contact unity team for this';
                return true;
            }
        } else {
            $daewoo = SellerCourierDaewoo::where('seller_id', $this->shipment->seller_id)->first();
        }


        if (empty($daewoo)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

            
        $url = 'https://codapi.daewoo.net.pk/api/booking/quickTrack?trackingNo='.$this->tracking_number;
        
        $this->request_dump = $url;
        $this->get_url = $url;

        $curl = curl_init();

        curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        ));

        $response = curl_exec($curl);

        $this->response_dump = $response;
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['Result']) && $response['Result']['Success'] == true && isset($response['Result']['TrackingDetails']) && count($response['Result']['TrackingDetails']) > 0) {

            foreach ($response['Result']['TrackingDetails'] as $key => $detail) {

                if ($detail) {
                    if (isset($detail['Date'])) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::createFromFormat('d/m/Y g:i A',$detail['Date']);
                        $message = 'Last Courier Status : '.$detail['Status_Reason'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $temp_shipment_status_date, $detail['Status_Reason']  ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.( isset($detail['Status_Reason']) ? $detail['Status_Reason'] : 'Tracking detail not found');
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                    
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            if ($response['response']) {
                $message = json_encode($response['response']);
            } else {
                $message = 'Response message parameter is null';
            }
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    private function fly_courier()
    {
        $this->courier_name = 'FlyCourier';
        $this->log_name = 'FlyCourier '.$this->log_name;
        $message = '';

        $fly_courier = SellerCourierFlyCourier::where('seller_id', $this->shipment->seller_id)->first();


        if (empty($fly_courier)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

            
        $url = 'https://app.flycourier.com.pk/api/track';
        
        $this->get_url = $url;

        $params = [
            'shipment_no' => $this->tracking_number,
        ];      


        $headers = [ 'Content-Type' => 'application/json',
                     'Authorization' => $fly_courier->api_key];


        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers, 'form_params' => $params]);

        $response = $this->client->post($url, [
            'headers' => $headers,
            'body' => json_encode($params)
            ]); // Request Send

        $this->response_dump = $response;
        $response = $response->getBody()->getContents(); // Request Response
        Log::info($response);
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response['status']) && $response['status'] == 1 && isset($response['data']['tracking_details']) && isset($response['data']['tracking_details'][0]['tracking_info'])) {

            foreach ($response['data']['tracking_details'][0]['tracking_info'] as $key => $detail) {

                if ($detail) {
                    if (isset($detail['created_datetime'])) {
                        $error = 0;
                        $temp_shipment_status_date = Carbon::parse($detail['created_datetime']);
                        $message = 'Last Courier Status : '.$detail['shipment_status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-', $temp_shipment_status_date, $detail['shipment_status']  ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.( isset($detail['shipment_status']) ? $detail['shipment_status'] : 'Tracking detail not found');
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                    
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            if ($response['response']) {
                $message = json_encode($response['response']);
            } else {
                $message = 'Response message parameter is null';
            }
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    private function tqs()
    {
        $this->courier_name = 'TQS';
        $this->log_name = 'TQS '.$this->log_name;
        $message = '';

        $url = 'https://thequickservice.online/portal/API/TrackOrder.php';
        $params = [ 'tracking_no' => $this->tracking_number];
        $headers = [ 'Content-Type' => 'application/json'];
        
        $this->get_url = $url;
        $this->request_dump = json_encode(['url' => $url, 'headers' => $headers, 'form_params' => $params]);

        $response = $this->client->post($url, [
            'headers' => $headers,
            'body' => json_encode($params)
            ]); // Request Send

        $this->response_dump = $response;
        $response = $response->getBody()->getContents(); // Request Response
        Log::info($response);
        $response = json_decode($response, TRUE);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (is_array($response)) {

            foreach ($response as $key => $detail) {

                if ($detail) {
                    $error = 0;
                    $temp_shipment_status_date = Carbon::parse($detail['created']);
                    $message = 'Last Courier Status : '.$detail['status'].' | Last Status Time : '.$temp_shipment_status_date->toDateTimeString();
                    $status =  $detail['status'];
                    if (strpos($status, 'Delivered') !== false) {
                        $status = "Delivered";
                    }
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, $detail['status'], $temp_shipment_status_date, $status  ) );

                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            $message = json_encode($response);
        }
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    private function dex()
    {
        $this->courier_name = 'DEX';
        $this->log_name = 'DEX '.$this->log_name;
        $message = '';

        $dex_account = SellerCourierDEX::where('seller_id',  $this->shipment->seller_id)->first();

        if (empty($dex_account)) {
            $this->message = $this->log_name.' | Courier Credentials not found';
            return true;
        }

            
        $app_key = env('DEX_API_KEY');
        $app_secret =  env('DEX_API_SECRET');
        $api_name = '/logistics/epis/packages/history';

        $params = [
            'includeTimeline' => 'true',
            'trackingNumber' => $this->tracking_number,
            'app_key' => $app_key,
            'sign_method' => 'sha256',
            'sign' => 'to_be_excluded' // Exclude this while generating the signature
        ];
        $current_timestamp = round(microtime(true) * 1000); // Get current timestamp in milliseconds

        // Generate the signature
        $params['sign'] = $dex_account::generateDarazSignature($app_secret, $api_name, $params ,$current_timestamp);
        $params['timestamp'] = $current_timestamp;

        // URL-encode the parameters for the API call
        $query_string = http_build_query($params);

        Log::info($params);

        $response = $this->client->request('GET', env('DEX_API_URL') . $api_name, [
            'query' => $query_string,
        ]);

        $response_body = $response->getBody()->getContents();
        $response_body = json_decode($response_body, TRUE);


        Log::info($response_body);

        $local_tracking_array = [];
        $error = $this->error;

        // Reading response data
        if (isset($response_body['success']) && $response_body['success'] == true && isset($response_body['data']['timeline']) && count($response_body['data']['timeline']) > 0 ) {

            foreach ($response_body['data']['timeline'] as $key => $detail) {

                if ($detail) {
                    if (isset($detail['processTime'])) {
                        $error = 0;
                        $process_time = floor($detail['processTime'] / 1000);
                        $temp_shipment_status_date = Carbon::createFromTimestamp($process_time);
                        $message = 'Last Courier Status : '.$detail['status'].' | Last Status Time : '.$temp_shipment_status_date;
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(0, '-' , $temp_shipment_status_date, $detail['status']  ) );
                    } else {
                        $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | '.( isset($detail['status']) ? $detail['status'] : 'Tracking detail not found');
                        array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                    }
                    
                } else {
                    $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | DEX Error | '.' | Tracking detail not found at index #'.$key;
                    array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
                }
            }

        } else {
            $message = 'Unity Retail | '.$this->log_name.' | Shipment #'.$this->tracking_number.' | DEX Message | '.$response_body['errors']['errorMessage'];
            array_push($local_tracking_array, $this->generatingTrackingDetailsArray(1,$message) );
        }   
        
        $this->message = $message;
        $this->tracking_array = $local_tracking_array;
        $this->error = $error;

        return true;
    }

    public static function bykea($data, $message = null)
    {
        try {

            $data = (object) $data;

            if (stripos($data->event,'booking') !== false) {

                if (!isset($data->data['trip_id'])) {
                    return response()->json(['message' => 'Trip Id not found'], 400);
                }
                
                $shipment = Shipment::on('mysql3')->whereTrackingNumber($data->data['trip_id']);
                if ($shipment->exists()) {
                    $shipment = $shipment->first();

                    $message = 'Trip ID : '.$data->data['trip_id'].' | ';
                    $shipment_status_date = Carbon::createFromTimestamp(($data->event_time)/1000)->toDateTimeString(); 

                    if ($data->event == 'booking.created') {  // Booking created
                        $message .= 'Booking created on bykea portal';

                    } elseif ($data->event == 'booking.updated.trackinglink') {  // tracking started
                        $message .= 'Tracking link generated. <a target="blank" href="'.$data->data['tracking_link'].'">'.$data->data['tracking_link'].'</a>';

                    } elseif ($data->event == 'booking.accepted') {  // Booking accepted
                        $message .= 'Booking is accepted by a rider. | '.
                                    $data->data['partner']['name'].' | '.
                                    $data->data['partner']['plate_no'].' | '.
                                    $data->data['partner']['mobile'];

                    } elseif ($data->event == 'booking.arrived') {  // Booking arrived
                        $message .= 'Rider has arrived at your pickup location.';
                        $shipment->update(['status' => config('enum.shipment_status')['DISPATCHED']]);
                        $shipment->shipment_history(config('enum.shipment_status')['DISPATCHED'], ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                    
                    } elseif ($data->event == 'booking.started') {  // Booking started
                        $message .= 'Package has dispatch , Rider on its way to delivery.';
                        $shipment->update(['status' => config('enum.shipment_status')['PENDING_DELIVERY']]);
                        $shipment->shipment_history(config('enum.shipment_status')['PENDING_DELIVERY'], ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                    
                    } elseif ($data->event == 'booking.finished') { // Booking finished
                        $message .= 'Package has been delivered';
                        $shipment->update(['delivered_at' => $shipment_status_date, 'status' => config('enum.shipment_status')['DELIVERED']]);
                        $shipment->shipment_history(config('enum.shipment_status')['DELIVERED'], ($shipment_status_date ? $shipment_status_date : Carbon::now()->toDateTimeString()) );
                    
                        foreach ($shipment->items as $shipment_item) {
                            $shipment_item->order_item->update(['status' => config('enum.item_status')['COMPLETED']]);
                        }
        
                        $shipment->order->update_status_according_to_items();

                    } else { // Event not found
                        Mail::raw('Invalid Event | '.$data->event, function ($m)  {
                            $m->to('<EMAIL>')->subject('Bykea Tracking');
                        });
                        Log::info('Invalid Event | '.$data->event);
                    }
                    
                    $shipment->shipment_courier_history($data->event, $shipment_status_date, $message);

                } else {
                    return response()->json(['message' => 'Trip Id not exists in our list'], 400);
                }


            } elseif (stripos($data->event,'batch') !== false) {
                
                if (!isset($data->data['batch_id'])) {
                    return response()->json(['message' => 'Batch Id not found'], 400);
                }

                $batch = DB::connection('mysql2')->table('extra_columns')->where('keyable_type','App\Models\Shipment')->where('value',$data->data['batch_id']);
                if ($batch->exists()) {
                    $batch = $batch->first();

                    $shipment = Shipment::find($batch->keyable_id);
                    $message = 'Batch ID : '.$data->data['batch_id'].' | ';
                    $shipment_status_date = Carbon::createFromTimestamp(($data->event_time)/1000)->toDateTimeString(); 

                    if ($data->event == 'batch.created') {  // Booking created
                        $message .= 'Batch created on bykea portal';

                    } elseif ($data->event == 'batch.cancelled.partner') {  // batch cancel
                        $message .= 'Previous rider has cancel the delivery request , now assigning another rider . ';

                    } elseif ($data->event == 'batch.accepted') {  // batch accepted
                        $message .= 'Batch is accepted by a rider.';

                    } elseif ($data->event == 'batch.arrived') {  // Batch arrived
                        $message .= 'Rider has arrived at your pickup location.';
                    
                    } elseif ($data->event == 'batch.started') {  // Batch started
                        $message .= 'Package has dispatch , Rider on its way to delivery.';
                    
                    } elseif ($data->event == 'batch.completed') { // Batch finished
                        $message .= 'Batch has been completed';
                    
                    } elseif ($data->event == 'batch.cancelled.customer') { // Batch finished
                        $message .= 'Batch has been cancelled by shipper itself';
                    
                    } else { // Event not found
                        Mail::raw('Invalid Event | '.$data->event, function ($m)  {
                            $m->to('<EMAIL>')->subject('Bykea Tracking');
                        });
                        Log::info('Invalid Event | '.$data->event);
                    }
                    
                    $shipment->shipment_courier_history($data->event, $shipment_status_date, $message);

                } else {
                    return response()->json(['message' => 'Batch Id not exists in our list'], 400);
                }
            } else {
                Mail::raw('Invalid Event | '.$data->event, function ($m)  {
                    $m->to('<EMAIL>')->subject('Bykea Tracking');
                });
                Log::info('Invalid Event | '.$data->event);
                return response()->json(['message' => 'Invalid Event'], 400);
            }

        } catch (Exception $e) {
            Mail::raw($e->getMessage(), function ($m)  {
                $m->to('<EMAIL>')->subject('Bykea Tracking');
            });
            Log::info($e->getTraceAsString());
            return response()->json(['message' => 'Internal error'], 500);
        }

        return response()->json(['message' => 'Success'], 200);
    }

    private function generatingTrackingDetailsArray($error, $message, $status_date=null, $status=null)
    {
        if ($this->tracking_type == config('enum.tracking_types')['ONLY-RESPONSE'] && $status_date) {
            $status_date = $status_date->toDateTimeString();
        }
        return compact('error','message','status_date','status');
    }

    private function shipmentCourierHistoryPreviousStatus($status)
    {
        $old_status = ShipmentCourierHistory::on('mysql2')->whereShipmentId($this->shipment->id)->latest()->value('status');
        if(isset($old_status)) {
            if($old_status == $status) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
}