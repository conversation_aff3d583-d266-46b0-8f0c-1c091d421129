<?php

namespace App\Traits;

use Exception;
use Illuminate\Support\Facades\Log;
use Spatie\Activitylog\Contracts\Activity;

trait LoggingTrait
{
    public function addLog($seller_id, $model_instance, $dump, $log_name)
    {
        $error = 0;
        $message = 'Success';

        try {
            $activity_id = activity()
                            ->performedOn($model_instance)
                            // ->causedBy($seller_id)
                            ->tap(function(Activity $activity) use ($seller_id) {
                                $activity->causer_id = $seller_id;
                            })
                            ->withProperties($dump)
                            ->log($log_name);
        } catch (Exception $e) {
            $error = 1;
            $message = $e->getMessage();
            Log::error('Exception while adding activity log | '.$e->getMessage());
            Log::error($e->getTraceAsString());
        }

        return compact('error', 'message');
    }

    public function addArrayLog($array, $seller_id, $model_instance, $log_name, $dump = null)
    {
        $error = 0;
        $message = 'Success';

        try {

            foreach ($array as $key => $value) {
                
                if (!is_array($dump)) {
                    $dump = ($dump ? json_decode($dump, true) : []);
                }
                
                if (is_string($value)) {
                    $dump['message'] = $value;
                } else {
                    $dump['message'] = json_encode($value);
                }

                $this->addLog($seller_id, $model_instance, $dump, $log_name);
            }

        } catch (Exception $e) {
            $error = 1;
            $message = $e->getMessage();
            Log::error('Exception while adding multiple activity log | '.$e->getMessage());
            Log::error($e->getTraceAsString());
        }

        return compact('error', 'message');
    }
}