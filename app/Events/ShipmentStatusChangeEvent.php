<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ShipmentStatusChangeEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $shipment_id, $shipment_status, $status_id;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($shipment_id, $shipment_status, $status_id)
    {
        $this->shipment_id = $shipment_id;
        $this->shipment_status = $shipment_status;
        $this->status_id = $status_id;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
