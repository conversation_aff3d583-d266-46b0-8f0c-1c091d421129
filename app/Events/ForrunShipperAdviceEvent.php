<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ForrunShipperAdviceEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public $seller_id,$tracking_number,$status;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($seller_id, $tracking_number,$status)
    {
        $this->seller_id = $seller_id;
        $this->tracking_number = $tracking_number;
        $this->status = $status;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
