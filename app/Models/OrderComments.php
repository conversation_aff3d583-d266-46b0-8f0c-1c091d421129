<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderComments extends Model
{
    protected $connection = 'mysql3';

    static public function add($id, $key, $value, $status, $run_by)
    {
        $orderComments = new self;
        $orderComments->order_id = $id;
        $orderComments->key = $key;
        $orderComments->value = $value;
        $orderComments->status = $status;
        $orderComments->run_by = $run_by;
        $orderComments->save();
    }
}
