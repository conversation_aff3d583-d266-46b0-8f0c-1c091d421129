<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Exception extends Model
{
    protected $connection = 'mysql3';

    public function type() {
        return $this->belongsTo('App\Models\ExceptionType', 'type_id', 'id')->select('id','name','detail');
    }


    public function shipment() {
        return $this->belongsTo('App\Models\Shipment', 'entity_id', 'id')->select('id','order_id');
    }
}
