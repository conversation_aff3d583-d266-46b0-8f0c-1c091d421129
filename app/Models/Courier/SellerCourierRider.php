<?php

namespace App\Models\Courier;

use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Model;

class SellerCourierRider extends Model
{
    protected $connection = 'mysql2';

    static public function track($shipment)
    {
        $error = 1;
        $message = '';
        $tracking_array = [];

        try {
            $client = new Client();
            $rider = SellerCourierRider::where('seller_id', $shipment->seller_id)->where('courier_id', 11)->first();

            $response = $client->get('http://api.withrider.com/rider/v1/GetStatusHistory?cn='.$shipment->tracking_number.'&phone='.$shipment->order->customer_number.'&loginId='.$rider->login_id.'&apikey='.$rider->api_key);
            $response = $response->getBody()->getContents();


            if ($response) {
                $response = json_decode($response, TRUE);

                foreach (array_reverse($response) as $key => $value) {
                
                    if(isset($value['order_status'])) {

                        $status = $value['order_status'];
                        $shipment_status_date = Carbon::parse($value['updated_at'])->toDateTimeString();
                        $status_detail = $value['reason'];
                        
                        $error = 0;
                        array_push($tracking_array,$shipment_status_date."<br>".$status."<br>".$status_detail);
                    }
                    else{
                        $message .= 'No tracking details found';
                    }
                }
            }
            else{
                $message .= 'No tracking details found';
            }
        } catch(Exception $e) {
            $message .= 'Got Error: '.$e;
        }

        if($error == 1){
            return array('error' => 1, 'data' => $message);
        }
        else{
            return array('error' => 0, 'data' => $tracking_array);
        }
    }
}
