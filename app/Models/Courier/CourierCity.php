<?php

namespace App\Models\Courier;

use Illuminate\Database\Eloquent\Model;

class CourierCity extends Model
{
    protected $connection = 'mysql2';

    protected $table = 'courier_cities';
    public $timestamps = false;

    public function courier()
    {
        return $this->belongsTo(\App\Models\Courier\Courier::class,'courier_id','id')->select('id','name');
    }

    public function city()
    {
        return $this->belongsTo('App\Models\City','city_id');
    }
}
