<?php

namespace App\Models\Courier;

use Illuminate\Database\Eloquent\Model;

class SellerCourierDEX extends Model
{
    protected $connection = 'mysql2';

    protected $table = 'seller_courier_dexs';

    static public function generateDarazSignature($app_secret, $api_name, $params ,$current_timestamp)
    {
        $params['timestamp'] = $current_timestamp;
        
        // Step 1: Sort the parameters by ASCII order, excluding 'sign'
        $sorted_params = collect($params)->except(['sign'])->sortKeys()->toArray();
    
        // Step 2: Concatenate the sorted parameters and their values
        $concatenated_params = '';
        foreach ($sorted_params as $key => $value) {
            $concatenated_params .= $key . $value;
        }
    
        // Step 3: Add the API name in front of the concatenated string
        $string_to_sign = $api_name . $concatenated_params;
    
        // Step 4: Generate the HMAC-SHA256 digest
        $hash = hash_hmac('sha256', $string_to_sign, $app_secret);
    
        return strtoupper($hash);
    }

}
