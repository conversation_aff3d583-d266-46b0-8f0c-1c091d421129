<?php

namespace App\Models\Courier;

use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Model;

class SellerCourierTraxNew extends Model
{
    protected $connection = 'mysql2';

    protected $table = 'seller_courier_trax_new';

    static public function track($shipment)
    {
        $error = 1;
        $message = '';
        $tracking_array = [];

        try {
            $client = new Client();
            $TraxNew = SellerCourierTraxNew::where('seller_id', $shipment->seller_id)->where('courier_id', 8)->first();

            $response = $client->get('https://sonic.pk/api/shipment/track?tracking_number='.$shipment->tracking_number.'&type=1', [
                'headers' => [ 'Authorization' => $TraxNew->authorization_key ]
            ]);
            $response = $response->getBody()->getContents();
            $response = json_decode($response, TRUE);


            if ($response['status'] == 0) {

                foreach ($response['details']['tracking_history'] as $key => $value) {
                
                    $status = $value['status'];
                    $shipment_status_date = Carbon::createFromFormat('d/m/Y g:i A',$value['date_time'])->toDateTimeString();
                    $status_detail = $value['status_reason'];
                    $error = 0;
                    array_push($tracking_array,$shipment_status_date.'<br>'.$status.'<br>'.$status_detail);
                }
            }
            else{
                $message .= 'No tracking details found';
            }
        } catch(Exception $e) {
            $message .= 'Got Error: '.$e;
        }

        if($error == 1){
            return array('error' => 1, 'data' => $message);
        }
        else{
            return array('error' => 0, 'data' => $tracking_array);
        }
    }
}
