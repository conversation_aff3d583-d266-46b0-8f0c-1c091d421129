<?php

namespace App\Models\Courier;

use Carbon\Carbon;
use Exception;
use <PERSON>uz<PERSON><PERSON>ttp\Client;
use <PERSON>uzzleHttp\Exception\RequestException;
use Illuminate\Database\Eloquent\Model;

class SellerCourierMNP extends Model
{
    protected $connection = 'mysql2';
    protected $table = 'seller_courier_mnp';

    protected $only_response;
    protected $shipment_update;
    protected $show_exception;
    protected $logging_enabled;


    public function __construct($only_response = true, $shipment_update = false, $show_exception = false, $logging_enabled = true) {
        $this->only_response = $only_response;
        $this->shipment_update = $shipment_update;
        $this->show_exception = $show_exception;
        $this->logging_enabled = $logging_enabled;
    }

    public function track($shipment)
    {
        $error = 1;
        $message = '';
        $details_not_found = false;
        $tracking_array = [];
        $comment_array = [];
        $exception_array = [];
        $logging_array = [];

        try {
            $temp  = $this->call_track($shipment->tracking_number, $error, $message, $details_not_found, $tracking_array);
            $error = $temp['error'];
            $message = $temp['message'];
            $details_not_found = $temp['details_not_found'];
            $tracking_array = $temp['tracking_array'];
            
        } catch(RequestException $e) {

            if (method_exists($e, 'getResponse')) {
                if ($e->getResponse()) {
                    $error_response = json_decode(((string) $e->getResponse()->getBody()), true);
                    dd($error_response);
    
                    if ( isset($error_response['error']) ) {
                        $temp_error = $error_response['error'];
                    } elseif ( isset($error_response['message']) ) {
                        $temp_error = json_encode($error_response['message']);
                    } else {
                        $temp_error = $e->getMessage();
                    }
                } else {
                    $temp_error = $e->getMessage();
                }
            } else {
                $temp_error = $e->getMessage();
            }

            $message .= 'Got Request Exception Error: '.$temp_error;
            array_push($logging_array , ['error' => $message, 'skipTrace' => $e->getTraceAsString()]);
            
        } catch(Exception $e) {
            $message .= 'Got Exception Error: '.$e->getMessage();
            array_push($logging_array , ['error' => $message, 'skipTrace' => $e->getTraceAsString()]);
        }

        if ($error == 0) {
            if ($this->only_response) {
                $data = $tracking_array;
            } else {
                $data = 'non';
            }
        } else {
            $data = $message;
        }

        return compact('error','data');
    }

    public function call_track($tracking_number, $error, $message, $details_not_found, $tracking_array)
    {
        $client = new Client();
        $mnp_url = 'http://mnpcourier.com/mycodapi/api/Tracking/Tracking?consignment=12'.$tracking_number;
        $response = $client->get($mnp_url);

        $response = $response->getBody()->getContents();
        $response = json_decode($response, TRUE);

        if (isset($response[0]['isSuccess']) && $response[0]['isSuccess'] == 'true') {

            if (empty($response[0]['tracking_Details'][0]['Details'])) {
                $status = $response[0]['tracking_Details'][0]['CNStatus'];
                $status_date = Carbon::now();
                $detail = 'Details not found against this shipment, shipment overall status is used';
                $details_not_found = true;
                $error = 0;

                $temp = compact('error', 'message', 'details_not_found', 'status_date', 'status', 'detail');
                array_push($tracking_array, $temp );
            } else {
                foreach ($response[0]['tracking_Details'][0]['Details'] as $key => $detail) {
                    if ($detail) {
                        $status = $detail['Status'];
                        $status_date = Carbon::parse($detail['DateTime']);
                        $detail = $detail['Detail'];
                        $error = 0;

                        $temp = compact('error', 'message', 'details_not_found', 'status_date', 'status', 'detail');
                    } else {
                        $message .= 'Tracking detail not found at index #'.$key;
                        $temp = compact('error', 'message');
                    }
                    array_push($tracking_array, $temp );
                }
            }
        } else {
            if (isset($response[0]['message'])) {
                $message .= 'MnP Response | '.$response[0]['message'];
            } else {
                $message .= 'IsSuccess in Response contains False';
            }
        }
        return compact('error', 'message', 'details_not_found', 'tracking_array');
    }
}
