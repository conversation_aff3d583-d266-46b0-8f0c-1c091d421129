<?php

namespace App\Models;

use App\Events\ShipmentStatusChangeEvent;
use App\Service\ForceDispatchService;
use Illuminate\Database\Eloquent\Model;
use App\Models\ShipmentCourierHistory;
use App\Models\ShipmentHistory;

class Shipment extends Model
{
    protected $connection = 'mysql2';

    protected $fillable = ['delivered_at', 'status'];

    public function order()
    {
        return $this->belongsTo('App\Models\Order');
    }

    public function orderForReports()
    {
        return $this->belongsTo('App\Models\Order','order_id','id')->select('id','marketplace_reference_id');
    }

    public function shipment_booking_courier() {
        return $this->hasOne('App\Models\ShipmentExtra')->select('id','shipment_id','key','value')->where('key','booking_courier')->withDefault(['value' => 0]);
    }

    public function seller()
    {
        return $this->belongsTo('App\Models\Seller');
    }

    public function items()
    {
    	return $this->hasMany('App\Models\ShipmentItem');
    }

    public function courier() {
        return $this->belongsTo('App\Models\Courier\Courier', 'courier_id', 'id')->select('id','name');
    }

    public function shipment_history($status, $date)
    {
        if ( in_array($this->status, [config('enum.shipment_status')['BOOKED'], 'Ready for Dispatch']) && !in_array($status, [config('enum.shipment_status')['BOOKED'], 'Ready for Dispatch', 'Cancelled'])) {
            ForceDispatchService::execute($this->tracking_number, $this->seller_id, $status);
        }

        $shipmentHistory = new ShipmentHistory;
        $shipmentHistory->shipment_id = $this->id;
        $shipmentHistory->status = $status;
        $shipmentHistory->status_at = $date;
        $shipmentHistory->save();

        if ($this->type == Null) {
            OrderComments::add($this->order_id, 'Shipment Tracking Process', 'Shipment #'.$this->tracking_number.' is now has a <b>'.$status.'</b> status', 'Success', '1' );
        }

        event(new ShipmentStatusChangeEvent($this->id, $status, $shipmentHistory->id));
    }

    public function shipment_courier_history($status, $date, $message = '-')
    {
        if ($this->courier_id == 17) {
            $status = isset(config('courierstatuses.dhl-full-status')[$status]) ? config('courierstatuses.dhl-full-status')[$status] : $status ;
        }

        if ($this->courier_id == 23) {
            $status = isset(config('courierstatuses.quiqup_full_statuses')[$status]) ? config('courierstatuses.quiqup_full_statuses')[$status] : $status ;
        }

        if ($this->courier_id == 30) {
            $status = isset(config('courierstatuses.time-express-full-status')[$status]) ? config('courierstatuses.time-express-full-status')[$status] : $status ;
        }

        
        if (! ShipmentCourierHistory::on('mysql2')->where('shipment_id',$this->id)->where('status',$status)->where('message',$message)->where('status_at',$date)->exists()) {

            $shipmentHistory = new ShipmentCourierHistory;
            $shipmentHistory->shipment_id = $this->id;
            $shipmentHistory->status = $status;
            $shipmentHistory->message = $message;
            $shipmentHistory->status_at = $date;
            $shipmentHistory->save();
        }

        Shipment::on('mysql3')->where('id',$this->id)->update(['last_run' => $date]);
    }
}
