<?php

namespace App\Models;

use App\Models\OrderComments;
use App\Models\Seller;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class WalletTransaction extends Model
{
    use HasFactory;
    protected $connection = 'mysql3';

    public static function add($type, $order_id, $wallet, $new_wallet, $charges, $message, $seller_id, $cn)
    {
        $transaction = new self;
        $transaction->wallet_transaction_type_id = $type;
        $transaction->order_id = $order_id;
        $transaction->seller_id = $seller_id;
        $transaction->cn = $cn;
        $transaction->credits_before = $wallet;
        $transaction->credits_after = $new_wallet;
        $transaction->amount_charged = $charges;
        $transaction->remarks = $message;
        $transaction->save();
        return $transaction->id;
    }

    public static function reCharged($charges, $order_id, $seller_id, $cn)
    {
        $error = 1;
        $message = '';

        if (!self::pickForUpdate($seller_id)) {
            $message = 'Wallet amount must be greater than shipping charges amount '.$charges;

        } else {

            $credits_before = Seller::whereId($seller_id)->value('wallet');
            $credits_after = $credits_before-$charges;
            DB::connection('mysql3')->table('sellers')->where('id',$seller_id)->update(['pick_for_update' => 0, 'wallet' => $credits_after]);
            $message = $charges.' amount has been deducted from your wallet for this order booking, because previously deducted amount was re-adjusted due to shipment cancellation which is actually moves forward to its destination';
            OrderComments::add($order_id, 'Wallet Transaction Process', $message, 'Success', 1);
            self::add(2, $order_id, $credits_before, $credits_after, $charges, $message, $seller_id, $cn);
            $error = 0;

            Mail::raw($message.' | Tracking Number : '.$cn.' | Credits After : '.$credits_after, function ($m) {
                $m->to('<EMAIL>')
                ->bcc('<EMAIL>')
                ->subject('Cancel Universal Shipment Wallet Amount Deduction');
            });
        }

        return compact('error', 'message');
    }

    public static function pickForUpdate($seller_id)
    {
        if (DB::connection('mysql3')->table('sellers')->where('id',$seller_id)->where('pick_for_update',0)->update(['pick_for_update' => 1])) {
            return true;
        } else {
            sleep(1);
            return self::pickForUpdate($seller_id);
        }
    }
}