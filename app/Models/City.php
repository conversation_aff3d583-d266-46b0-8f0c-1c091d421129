<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class City extends Model
{
    protected $connection = 'mysql3';

    protected $table = 'cities';

    public $timestamps = false;

    protected $fillable = array('id', 'name', 'state_or_province_id');

    public function state_or_province() {
        return $this->belongsTo('App\Models\StateOrProvince', 'state_or_province_id', 'id');
    }

    public function courierCity() {
        return $this->hasMany('App\Models\Courier\CourierCity', 'city_id');
    }
}
