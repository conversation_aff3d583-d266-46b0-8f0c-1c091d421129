<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentWeightDescripancy extends Model
{
    protected $connection = 'mysql3';

    protected $table = 'shipment_weight_discrepancies';

    protected $fillable = ['seller_id', 'shipment_id', 'original_weight', 'courier_booked_weight','courier_arival_weight'];

    public static function add($seller_id, $shipment_id, $original_weight, $courier_booked_weight, $courier_arival_weight)
    {
        $shipment_weight_descripancy = new self;
        $shipment_weight_descripancy->seller_id = $seller_id;
        $shipment_weight_descripancy->shipment_id = $shipment_id;
        $shipment_weight_descripancy->original_weight = $original_weight;
        $shipment_weight_descripancy->courier_booked_weight = $courier_booked_weight;
        $shipment_weight_descripancy->courier_arival_weight = $courier_arival_weight;
        $shipment_weight_descripancy->save();
    }
}
