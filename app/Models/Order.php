<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Order extends Model
{
    protected $connection = 'mysql2';

	protected $fillable = ['status','seller_id','seller_location_id','marketplace_reference_id'];
	
	public $timestamps = false;

    public function items()
	{
		return $this->hasMany('App\Models\OrderItem');
    }
    
	public function shipments()
	{
		return $this->hasMany('App\Models\Shipment');
	}

	public static function re_evaluate_status($order_id)
	{
		$change_status = FALSE;
		$count = 0;
		$done = 0;

		$pending = 0;
		$processing = 0;
		$completed = 0;
		$cancelled = 0;

		$order_items = OrderItem::on('mysql3')->where('order_id',$order_id)->get(['status']);
		
		foreach ($order_items as $item) {
			if ($item->status == config('enum.item_status')['PENDING']) {
				$done++;
				$pending++;
				$change_status = TRUE;
			} else if ($item->status == config('enum.item_status')['DISPATCHED']) {
				$done++;
				$processing++;
				$change_status = TRUE;
			} else if ( in_array($item->status, [config('enum.item_status')['RETURNED'], config('enum.item_status')['COMPLETED']]) ) {
				$done++;
				$completed++;
				$change_status = TRUE;
			} else if ($item->status == config('enum.item_status')['CANCELLED']) {
				$done++;
				$cancelled++;
				$change_status = TRUE;
			} else {
				$change_status = FALSE;
				break;
			}
			$count++;
		}

		if ($change_status) {
			if ($count == $done) {

				$order_status = null;

				if ($count == $pending) {
					$order_status = config('enum.order_status')['PENDING'];
				} elseif ($count == $processing) {
					$order_status = config('enum.order_status')['PROCESSING'];
				} elseif ($count == $completed) {
					$order_status = config('enum.order_status')['COMPLETED'];
				} elseif ($count == $cancelled) {
					$order_status = config('enum.order_status')['CANCELLED'];
				} else {
					if ($processing) {
						$order_status = config('enum.order_status')['PROCESSING'];
					} elseif ($count == ($completed+$cancelled) ) {
						$order_status = config('enum.order_status')['COMPLETED'];
					} elseif ($count == ($pending+$completed+$cancelled) ) {
						$order_status = config('enum.order_status')['PENDING'];
					}
				}

				if ($order_status) {

					if ($order_status == Order::on('mysql3')->where('id',$order_id)->value('status')) {
						return ['error' => 1, 'message' => 'Order status is already in '.$order_status.' state'];
					} else {
						Order::on('mysql3')->where('id',$order_id)->update(['status' => $order_status]);
						return ['error' => 0, 'message' => 'Order successfully updated ('.$order_status.')'];
					}
					
				} else {
					return ['error' => 1, 'message' => 'Order status ('.$order_status.') is null'];
				}
			}
		}

		return ['error' => 1, 'message' => 'Order is not eligible to update'];
	}
}
