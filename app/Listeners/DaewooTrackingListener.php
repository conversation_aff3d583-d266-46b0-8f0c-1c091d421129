<?php

namespace App\Listeners;

use App\Events\DaewooTrackingEvent;
use App\Events\TraxTrackingEvent;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\TrackingTrait;

class DaewooTrackingListener implements ShouldQueue
{

    private $client;
    public $queue = "daewooTracking";
    use TrackingTrait;

    /**
     * Handle the event.
     *
     * @param  ShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(DaewooTrackingEvent $event)
    {
        Log::info('Daewoo Sellers Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('Daewoo Sellers Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Daewoo Sellers Listener Ended');
    }

    public function failed(DaewooTrackingEvent $event, $exception) {
        Log::info( 'Daewoo Sellers Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
