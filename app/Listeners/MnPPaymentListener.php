<?php

namespace App\Listeners;

use App\Events\MnPPaymentEvent;
use App\Models\Courier\SellerCourierMNP;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class MnPPaymentListener implements ShouldQueue
{
    public $queue = "mnpPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  MnPPaymentEvent  $event
     * @return void
     */
    public function handle(MnPPaymentEvent $event)
    {
        Log::info('MnP Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $mnp = SellerCourierMnP::where('seller_id', $shipment->seller_id)->where('courier_id', 4)->first(['username','password','account_no']);
                
                if ($mnp) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('mnp', [ 'username' => $mnp->username, 'password' => $mnp->password, 'account_no' => $mnp->account_no] )));
                } else {
                    Log::info('MnP Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('MnP Payment Api'));
            }
        }
        Log::info('MnP Payment Listener Ended');
    }
}
