<?php

namespace App\Listeners;

use App\Events\MoveXPaymentEvent;
use App\Models\Courier\SellerCourierMoveX;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class MoveXPaymentListener implements ShouldQueue
{
    public $queue = "movexPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  MoveXPaymentEvent  $event
     * @return void
     */
    public function handle(MoveXPaymentEvent $event)
    {
        Log::info('MoveX Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {
            try {
                $movex = SellerCourierMoveX::where('seller_id', $shipment->seller_id)->where('courier_id', 19)->first(['api_key']);
                
                if ($movex) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('movex', [ 'api_key' => $movex->api_key] )));
                } else {
                    Log::info('MoveX Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                Log::info('MoveX Catch Block | '.$shipment);
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('MoveX Payment Api'));
            }
        }
        Log::info('MoveX Payment Listener Ended');
    }
}
