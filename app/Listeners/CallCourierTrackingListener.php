<?php

namespace App\Listeners;

use App\Events\CallCourierTrackingEvent;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\TrackingTrait;

class CallCourierTrackingListener implements ShouldQueue
{

    private $client;
    public $queue = "callCourierTracking";
    use TrackingTrait;

    /**
     * Handle the event.
     *
     * @param  ShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(CallCourierTrackingEvent $event)
    {
        Log::info('Call Courier Sellers Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('Call Courier Sellers Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Call Courier Sellers Listener Ended');
    }

    public function failed(CallCourierTrackingEvent $event, $exception) {
        Log::info( 'Call Courier Sellers Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
