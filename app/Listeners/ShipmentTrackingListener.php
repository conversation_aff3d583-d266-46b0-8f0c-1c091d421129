<?php

namespace App\Listeners;

use App\Events\ShipmentTrackingEvent;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\TrackingTrait;

class ShipmentTrackingListener implements ShouldQueue
{

    private $client;
    public $queue = "tracking";
    use TrackingTrait;

    /**
     * Handle the event.
     *
     * @param  ShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(ShipmentTrackingEvent $event)
    {
        Log::info('Normal Sellers Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('Normal Sellers Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Normal Sellers Listener Ended');
    }

    public function failed(ShipmentTrackingEvent $event, $exception) {
        Log::info( 'Normal Sellers Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
