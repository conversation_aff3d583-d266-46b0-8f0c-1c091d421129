<?php

namespace App\Listeners;

use App\Events\RiderPaymentEvent;
use App\Models\Courier\SellerCourierRider;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class RiderPaymentListener implements ShouldQueue
{
    public $queue = "riderPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  RiderPaymentEvent  $event
     * @return void
     */
    public function handle(RiderPaymentEvent $event)
    {
        Log::info('Rider Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $rider = SellerCourierRider::where('seller_id', $shipment->seller_id)->where('courier_id', 11)->first(['login_id','api_key']);
                
                if ($rider) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('rider', [ 'login_id' => $rider->login_id, 'api_key' => $rider->api_key] )));
                } else {
                    Log::info('Rider Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('Rider Payment Api'));
            }
        }
        Log::info('Rider Payment Listener Ended');
    }
}
