<?php

namespace App\Listeners;

use App\Events\LCSMerchantTrackingEvent;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\TrackingTrait;

class LCSMerchantTrackingListener implements ShouldQueue
{

    private $client;
    public $queue = "lcsMerchantTracking";
    use TrackingTrait;

    /**
     * Handle the event.
     *
     * @param  ShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(LCSMerchantTrackingEvent $event)
    {
        Log::info('LCSMerchant Sellers Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('LCSMerchant Sellers Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('LCSMerchant Sellers Listener Ended');
    }

    public function failed(LCSMerchantTrackingEvent $event, $exception) {
        Log::info( 'LCSMerchant Sellers Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
