<?php

namespace App\Listeners;

use App\Events\UniversalShipmentTrackingEvent;
use App\Models\Shipment;
use App\Traits\TrackingTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UniversalShipmentTrackingListener implements ShouldQueue
{
    private $client;
    public $queue = "universalTracking";
    use TrackingTrait;


    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  UniversalShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(UniversalShipmentTrackingEvent $event)
    {
        Log::info('Universal Shipment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('Universal Shipment Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Universal Shipment Listener Ended');
    }
}
