<?php

namespace App\Listeners;

use App\Events\BlueExPaymentEvent;
use App\Models\Courier\SellerCourierBlueEx;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class BlueExPaymentListener implements ShouldQueue
{
    public $queue = "blueExPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  BlueExPaymentEvent  $event
     * @return void
     */
    public function handle(BlueExPaymentEvent $event)
    {
        Log::info('Blue-Ex Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $BlueEx = SellerCourierBlueEx::where('seller_id', $shipment->seller_id)->where('courier_id', 10)->first(['account_no','user_id','password']);
                
                if ($BlueEx) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('blueEx', [ 'account_no' => $BlueEx->account_no, 'user_id' => $BlueEx->user_id, 'password' => $BlueEx->password, ] )));
                } else {
                    Log::info('Blue-Ex Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('Blue-Ex Payment Api'));
            }
        }
        Log::info('Blue-Ex Payment Listener Ended');
    }
}
