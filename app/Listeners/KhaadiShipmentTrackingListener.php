<?php

namespace App\Listeners;

use App\Events\KhaadiShipmentTrackingEvent;
use App\Models\Shipment;
use App\Traits\TrackingTrait;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class KhaadiShipmentTrackingListener implements ShouldQueue
{
    public $queue = "khaadiTracking";
    use TrackingTrait;

    public function handle(KhaadiShipmentTrackingEvent $event)
    {
        Log::info('Khaadi Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->get();
        foreach ($shipments as $shipment) {
            Log::info('Khaadi Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Khaadi Listener Ended');

    }

    public function failed(KhaadiShipmentTrackingEvent $event, $exception) {
        Log::info( 'Khaadi Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
