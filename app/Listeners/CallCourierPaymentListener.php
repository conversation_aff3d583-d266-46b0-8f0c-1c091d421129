<?php

namespace App\Listeners;

use App\Events\CallCourierPaymentEvent;
use App\Models\Courier\SellerCourierCallCourier;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CallCourierPaymentListener implements ShouldQueue
{
    public $queue = "callcourierPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  CallCourierPaymentEvent  $event
     * @return void
     */
    public function handle(CallCourierPaymentEvent $event)
    {
        Log::info('Call Courier Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $call_courier = SellerCourierCallCourier::where('seller_id', $shipment->seller_id)->where('courier_id', 7)->first(['username','password','account_id']);
                
                if ($call_courier) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('call_courier', [ 'username' => $call_courier->username, 'password' => $call_courier->password,'account_id' => $call_courier->account_id] )));
                } else {
                    Log::info('Call Courier Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('Call Courier Payment Api'));
            }
        }
        Log::info('Call Courier Payment Listener Ended');
    }
}
