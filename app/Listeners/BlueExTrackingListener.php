<?php

namespace App\Listeners;

use App\Events\BlueExTrackingEvent;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\TrackingTrait;

class BlueExTrackingListener implements ShouldQueue
{

    private $client;
    public $queue = "blueExTracking";
    use TrackingTrait;

    /**
     * Handle the event.
     *
     * @param  ShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(BlueExTrackingEvent $event)
    {
        Log::info('BlueEx Sellers Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('BlueEx Sellers Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('BlueEx Sellers Listener Ended');
    }

    public function failed(BlueExTrackingEvent $event, $exception) {
        Log::info( 'BlueEx Sellers Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
