<?php

namespace App\Listeners;

use App\Events\ShipmentStatusChangeEvent;
use App\Models\Shipment;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ShipmentStatusChangeListener implements ShouldQueue
{
    public $queue = "shipment_status_change_trigger";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * Handle the event.
     *
     * @param  ShipmentStatusChangeEvent  $event
     * @return void
     */
    public function handle(ShipmentStatusChangeEvent $event)
    {
        $shipment_id = $event->shipment_id;
        $shipment_status = $event->shipment_status;

        $shipment = Shipment::whereId($shipment_id)->first();

        $auth_key = base64_encode($shipment->order->marketplace_reference_id);
        $auth_token = base64_encode(hash_hmac('sha256',$shipment->tracking_number,$auth_key,true));

        try{

            $response = $this->client->post(config('enum.endpoints')['UNITY'].'/api/shipment-status-change', [
                'http_errors' => FALSE,
                'headers' => [
                    'X-Auth-Token' => $auth_token
                ],
                'form_params' => [
                    'shipment_id' => $shipment->id,
                    'shipment_status' => $shipment_status,
                    'order_id' => $shipment->order_id,
                    'status_id' => $event->status_id
                ]
            ]);

            $response = $response->getBody()->getContents();
            $response = json_decode($response, TRUE);
                Log::info($response);
            if($response){
                if($response['error'] == 0){
                    Log::info('Shipment Status Change Event | Shipment ID: '.$shipment->id.' | Event Generated');
                }
                else{
                    Log::info('Shipment Status Change Event | Shipment ID: '.$shipment->id.' | Response Message: '.$response['message']);
                    // throw new Exception('Shipment Status Change Event | Shipment ID: '.$shipment->id.' | Response Message: '.$response['message']);
                }
            } else{
                Log::info('Shipment Status Change Event | Shipment ID: '.$shipment->id.' | Respose not found');
                // throw new Exception('Shipment Status Change Event | Shipment ID: '.$shipment->id.' | Respose not found');
            }
        } catch(Exception $e){
            $activity_id = activity()
            ->performedOn($shipment)
            ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
            ->log('Shipment Status Change Event');

            Mail::raw($e->getMessage(), function ($m)  {
                $m->to('<EMAIL>')->subject('Shipment Status Change Event');
            }); 

            Log::info('Shipment Status Change Event | Catch Block | '.$e->getMessage());
            // throw new Exception('Shipment Status Change Event | Catch Block');
        }
    }
}
