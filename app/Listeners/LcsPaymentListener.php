<?php

namespace App\Listeners;

use App\Events\LcsPaymentEvent;
use App\Models\Courier\SellerCourierLCS;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;

class LcsPaymentListener implements ShouldQueue
{
    public $queue = "lcsPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * Handle the event.
     *
     * @param  LcsPaymentEvent  $event
     * @return void
     */
    public function handle(LcsPaymentEvent $event)
    {
        Log::info('LCS Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $lcs = SellerCourierLCS::where('seller_id', $shipment->seller_id)->where('courier_id', 5)->first(['api_key','api_password']);
                
                if ($lcs) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('lcs', [ 'api_key' => $lcs->api_key, 'api_password' => $lcs->api_password] )));
                } else {
                    Log::info('LCS Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('LCS Payment Api'));
            }
        }
        Log::info('LCS Payment Listener Ended');
    }
}
