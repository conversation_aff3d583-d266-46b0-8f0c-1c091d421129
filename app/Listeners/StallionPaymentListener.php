<?php

namespace App\Listeners;

use App\Events\StallionPaymentEvent;
use App\Models\Courier\SellerCourierStallion;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class StallionPaymentListener implements ShouldQueue
{
    public $queue = "stallionPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  StallionPaymentEvent  $event
     * @return void
     */
    public function handle(StallionPaymentEvent $event)
    {
        Log::info('Stallion Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {
            try {
                $stallion = SellerCourierStallion::where('seller_id', $shipment->seller_id)->where('courier_id', 29)->first(['username','password']);
                
                if ($stallion) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('stallion', [ 'username' => $stallion->username, 'password' => $stallion->password] )));
                } else {
                    Log::info('Stallion Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                Log::info('Stallion Catch Block | '.$shipment);
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('Stallion Payment Api'));
            }
        }
        Log::info('Stallion Payment Listener Ended');
    }
}
