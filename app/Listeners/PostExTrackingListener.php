<?php

namespace App\Listeners;

use App\Events\PostExTrackingEvent;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\TrackingTrait;

class PostExTrackingListener implements ShouldQueue
{

    private $client;
    public $queue = "postExTracking";
    use TrackingTrait;

    /**
     * Handle the event.
     *
     * @param  ShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(PostExTrackingEvent $event)
    {
        Log::info('PostEx Sellers Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('PostEx Sellers Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('PostEx Sellers Listener Ended');
    }

    public function failed(PostExTrackingEvent $event, $exception) {
        Log::info( 'PostEx Sellers Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
