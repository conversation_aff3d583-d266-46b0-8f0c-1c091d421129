<?php

namespace App\Listeners;

use App\Events\TraxPaymentEvent;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class TraxPaymentListener implements ShouldQueue
{
    public $queue = "traxPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  TraxPaymentEvent  $event
     * @return void
     */
    public function handle(TraxPaymentEvent $event)
    {
        Log::info('Trax Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $trax = SellerCourierTraxNew::where('seller_id', $shipment->seller_id)->where('courier_id', 8)->first(['authorization_key']);
                
                if ($trax) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('trax', [ 'authorization_key' => $trax->authorization_key ] )));
                } else {
                    Log::info('Trax Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('Trax Payment Api'));
            }
        }
        Log::info('Trax Payment Listener Ended');
    }
}
