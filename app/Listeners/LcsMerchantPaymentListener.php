<?php

namespace App\Listeners;

use App\Events\LcsMerchantPaymentEvent;
use App\Models\Courier\SellerCourierLCS;
use App\Models\Courier\SellerCourierLCSMerchant;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;

class LcsMerchantPaymentListener implements ShouldQueue
{
    public $queue = "lcsMerchantPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * Handle the event.
     *
     * @param  LcsPaymentEvent  $event
     * @return void
     */
    public function handle(LcsMerchantPaymentEvent $event)
    {
        Log::info('LCS Merchant Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $lcs_merchant = SellerCourierLCSMerchant::where('seller_id', $shipment->seller_id)->where('courier_id', 35)->first(['api_key','api_password']);
                
                if ($lcs_merchant) {
                    $trackPayment = new TrackPayment($shipment);
                    Log::info(json_encode($trackPayment->callCourierFunction('lcs_merchant', [ 'api_key' => $lcs_merchant->api_key, 'api_password' => $lcs_merchant->api_password] )));
                } else {
                    Log::info('LCS Merchant Credentials Not Found | '.$shipment);
                }

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('LCS Merchant Payment Api'));
            }
        }
        Log::info('LCS Merchant Payment Listener Ended');
    }
}
