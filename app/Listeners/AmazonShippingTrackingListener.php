<?php

namespace App\Listeners;

use App\Events\AmazonShippingTrackingtEvent;
use App\Models\Shipment;
use App\Traits\TrackingTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class AmazonShippingTrackingListener implements ShouldQueue
{
    public $queue = "khaadiTrackingAmazonShipping";
    use TrackingTrait;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  AmazonShippingTrackingtEvent  $event
     * @return void
     */
    public function handle(AmazonShippingTrackingtEvent $event)
    {
        Log::info('Khaadi Listener(AmazonShipping) Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->get();
        foreach ($shipments as $shipment) {
            sleep(60);
            Log::info('Khaadi Listener(AmazonShipping) | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Khaadi Listener(AmazonShipping) Ended');
    }
    
    public function failed(AmazonShippingTrackingtEvent $event, $exception) {
        Log::info( 'Khaadi Listener(AmazonShipping) | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
