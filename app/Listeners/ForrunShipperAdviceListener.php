<?php

namespace App\Listeners;

use App\Events\ForrunShipperAdviceEvent;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ForrunShipperAdviceListener implements ShouldQueue
{
    public $queue = 'forrun_shipper_advice';
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ForrunShipperAdviceEvent  $event
     * @return void
     */
    public function handle(ForrunShipperAdviceEvent $event)
    {
        $seller_id = $event->seller_id;
        $tracking_number = $event->tracking_number;
        $status = $event->status;

        Log::info('Forrun Shipper Advice Listener Started | '.$tracking_number);

        try{
            $authSig = base64_encode(hash_hmac('sha256', 'URxNautIluS', 'yoitsasecretkey', true));

            $url = env('UNITY_URL').'/api/nautilus/forrun-shipper-advice';
        
            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
                "seller_id":'.$seller_id.',
                "tracking_number":"'.$tracking_number.'",
                "status":"'.$status.'"
            }',
            CURLOPT_HTTPHEADER => array(
                'X-Auth: '.$authSig,
                'Content-Type: application/json',
            ),
            ));

            $response = curl_exec($curl);
            $response = json_decode($response, TRUE);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            Log::info('Forrun Unity Response | '.json_encode($response));

            if($httpCode == 201){
                // $result_datas = $response['data'];
            
               
            } else{
            }

            curl_close($curl);
        } catch(Exception $e){
            
        }

        Log::info('Forrun Shipper Advice Listener Ended | '.$tracking_number);
    }
}
