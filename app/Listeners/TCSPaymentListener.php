<?php

namespace App\Listeners;

use App\Events\TCSPaymentEvent;
use App\Models\Courier\SellerCourierMoveX;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class TCSPaymentListener implements ShouldQueue
{
    public $queue = "tcsPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  TCSPaymentEvent  $event
     * @return void
     */
    public function handle(TCSPaymentEvent $event)
    {
        Log::info('TCS Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {
            try {
                // $movex = SellerCourierMoveX::where('seller_id', $shipment->seller_id)->where('courier_id', 19)->first(['api_key']);
                
              
                $trackPayment = new TrackPayment($shipment);
                Log::info(json_encode($trackPayment->callCourierFunction('tcs', [ 'client_id' => env('TCS_NEW_CLIENT_ID')] )));
                

            } catch(Exception $e) {
                Log::info('TCS Catch Block | '.$shipment);
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('TCS Payment Api'));
            }
        }
        Log::info('TCS Payment Listener Ended');
    }
}
