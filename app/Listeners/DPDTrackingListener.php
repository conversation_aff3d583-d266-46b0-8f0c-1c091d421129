<?php

namespace App\Listeners;

use App\Events\DPDTrackingtEvent;
use App\Models\Shipment;
use App\Traits\TrackingTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class DPDTrackingListener implements ShouldQueue
{
    public $queue = "khaadiTrackingDPD";
    use TrackingTrait;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  DPDTrackingtEvent  $event
     * @return void
     */
    public function handle(DPDTrackingtEvent $event)
    {
        Log::info('Khaadi Listener(DPD) Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->get();
        foreach ($shipments as $shipment) {
            sleep(60);
            Log::info('Khaadi Listener(DPD) | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Khaadi Listener(DPD) Ended');
    }
    
    public function failed(DPDTrackingtEvent $event, $exception) {
        Log::info( 'Khaadi Listener(DPD) | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
