<?php

namespace App\Listeners;

use App\Events\ShopifyMarkPaidEvent;
use App\Models\Order;
use App\Models\OrderComments;
use App\Service\ShopifyService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ShopifyMarkPaidListener implements ShouldQueue
{
    public $queue = "paid_on_shopify";

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  ShopifyMarkPaidEvent  $event
     * @return void
     */
    public function handle(ShopifyMarkPaidEvent $event)
    {
        $order_id = $event->order_id;
        $order = Order::find($order_id);
        try{
            if($order && $order->marketplace_id != null){
                $result = ShopifyService::markPaid($order);
                if($result['error'] == 0){
                    OrderComments::add($order->id, 'Shopify Mark Order Paid Process', $result['message'] , 'Success', '1');
                } else{
                    OrderComments::add($order->id, 'Shopify Mark Order Paid Process', $result['message'] , 'Failed', '1');
                }
            }
        } catch(Exception $e){
            OrderComments::add($order->id, 'Shopify Mark Order Paid Process', 'Something went wrong' , 'Failed', '1');
        }
    }
}
