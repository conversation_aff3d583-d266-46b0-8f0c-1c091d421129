<?php

namespace App\Listeners;

use App\Events\PostExPaymentEvent;
use App\Models\Courier\SellerCourierPostEx;
use App\Models\Shipment;
use App\Service\TrackPayment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;

class PostExPaymentListener implements ShouldQueue
{
    public $queue = "postexPayment";
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * Handle the event.
     *
     * @param  PostExPaymentEvent  $event
     * @return void
     */
    public function handle(PostExPaymentEvent $event)
    {
        Log::info('PostEx Payment Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipments)->where('cod_received',0)->get(['seller_id', 'tracking_number', 'id', 'order_id']);

        foreach ($shipments as $shipment) {

            try {
                $postex = SellerCourierPostEx::where('seller_id', $shipment->seller_id)->first();
                
                if (empty($postex)) {
                    Log::info('PostEx Credentials Not Found | '.$shipment);
                }

                $trackPayment = new TrackPayment($shipment);
                Log::info(json_encode($trackPayment->callCourierFunction('postex', [ 'token' => $postex->token, 'authorization' => $postex->authorization] )));

            } catch(Exception $e) {
                $activity_id = activity()
                ->performedOn($shipment)
                ->withProperties(['response' => $e->getMessage() , 'dump' => $e->getTraceAsString()])
                ->log(ucwords('PostEx Payment Api'));
            }
        }
        Log::info('PostEx Payment Listener Ended');
    }
}
