<?php

namespace App\Listeners;

use App\Events\TPLRiderTrackingEvent;
use App\Models\Shipment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\Traits\TrackingTrait;

class TPLRiderTrackingListener implements ShouldQueue
{

    private $client;
    public $queue = "tplRiderTracking";
    use TrackingTrait;

    /**
     * Handle the event.
     *
     * @param  ShipmentTrackingEvent  $event
     * @return void
     */
    public function handle(TPLRiderTrackingEvent $event)
    {
        Log::info('TPL Rider Sellers Listener Started');
        $shipments = Shipment::whereIn('id',$event->shipment)->with('shipment_booking_courier')->get();
        foreach ($shipments as $shipment) {
            Log::info('TPL Rider Sellers Listener | Tracking Number # '.$shipment->tracking_number.' | Courier ID : '.$shipment->courier_id);
            Log::info($this->trackSingle($shipment, $event->trackingType));
        }
        Log::info('Call Courier Sellers Listener Ended');
    }

    public function failed(TPLRiderTrackingEvent $event, $exception) {
        Log::info( 'TPL Rider Sellers Listener | '.(is_string($exception) ? $exception : $exception->getMessage()) );
        Log::info( $exception );
    }
}
