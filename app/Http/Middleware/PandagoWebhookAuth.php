<?php

namespace App\Http\Middleware;

use App\Models\Courier\SellerCourierPandaGo;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class PandagoWebhookAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        \Log::info('Pandago Tracking Payload on Callback URL | '.$request);
        // $auth_sig = hash_hmac('sha1', $request->all(), 'yoitsasecretkey');
        // return $auth_sig;
        $msg = '';
        $count = 0;
        if (!isset($request['order_id'])) {
            $msg.='order_id Parameter not found | ';
        } elseif (!isset($request['client_order_id'])) {
            $msg.='client_order_id Parameter not found | ';
        } elseif (!isset($request['status'])) {
            $msg.='status Parameter not found | ';
        } elseif (!isset($request['updated_at'])) {
            $msg.='updated_at Parameter not found | ';
        }

        if($msg != ''){
            // Mail::raw('Pandago Webhook | '.$msg, function ($m)  {
            //     $m->to('<EMAIL>')->subject('Pandago Tracking');
            // });

            return response()->json(['message' => $msg], 404);
        }

        
        return $next($request);
    }
}
