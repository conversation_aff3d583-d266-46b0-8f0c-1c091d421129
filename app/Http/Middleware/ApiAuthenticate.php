<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Log;

class ApiAuthenticate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $header_authentication=$request->header('Authorization');

        if (!$header_authentication) {
            Log::info("API | Authorization Not Found");
            return response()->json(['error' => 1, 'message' => 'Authorization Not Found'], 401);
        }
        $authSig = base64_encode(hash_hmac('sha256', $request->getContent(), env('NAUTILUS_KEY'), true));
	
        if ($header_authentication !== $authSig) {
            Log::info("API | Authentication Failed");
            return response()->json(['error' => 1, 'message' => 'Authorization Failed'], 401);
        }
        return $next($request);
    }
}
