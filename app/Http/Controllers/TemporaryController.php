<?php

namespace App\Http\Controllers;

use App\Models\FulfillmentOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Seller;
use App\Models\Shipment;
use Carbon\Carbon;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;

class TemporaryController extends Controller
{

    public function checkForDuplicates()
    {
        $shipments = Shipment::where('created_at','>',Carbon::now()->subMinutes(10)->toDateTimeString())
                            ->selectRaw('order_id, count(id) as shipment_count')
                            ->groupBy('order_id')
                            ->having('shipment_count','>',1)
                            ->get();

        Log::info('check for duplicates');

        if (count($shipments)) {
            $message = count($shipments).' orders shipments are generated more than 1 times in last 30 min | '.json_encode($shipments);
            Mail::raw($message, function ($m)  {
                $m->to(['<EMAIL>'])->subject('Per Order Duplicate Shipments');
            });
        }
    }

    public function checkForOrderDuplicates()
    {
        $orders = Order::where('created_date','>',Carbon::now()->subMinutes(10)->toDateTimeString())
                            ->selectRaw('marketplace_reference_id, seller_id, count(id) as order_count')
                            ->groupBy('marketplace_reference_id', 'seller_id')
                            ->having('order_count','>',1)
                            ->get();

        Log::info('check for order duplicates');

        if (count($orders)) {
            $message = count($orders).' orders are created more than 1 times in last 10 min | '.json_encode($orders);
            Mail::raw($message, function ($m)  {
                $m->to(['<EMAIL>'])->subject('Duplicate Order Per Seller');
            });

            foreach ($orders as $order_group) {
                // Find the latest duplicate order for this marketplace_reference_id
                $latest_order = Order::where('marketplace_reference_id', $order_group->marketplace_reference_id)
                    ->where('seller_id',$order_group->seller_id)
                    ->orderBy('id', 'desc') // Get the most recent order
                    ->first();
    
                if ($latest_order) {
    
                    $fulfilment_order = FulfillmentOrder::where('order_id',$latest_order->id)->where('status',0)->first();
    
                    if(!$fulfilment_order){
                        // Update seller_id to 1
                        Order::on('mysql3')
                        ->where('id', $latest_order->id)
                        ->update(['seller_id' => 2 , 'seller_location_id' => 1 , 'marketplace_reference_id' => $order_group->marketplace_reference_id."-DUPLICATE" ]);
                        // Update order_items (sku, barcode, product_id to null)
                        OrderItem::on('mysql3')->where('order_id', $latest_order->id)
                            ->update([
                                'sku' => null,
                                'barcode' => null,
                                'product_id' => null
                            ]);
                    }
    
                }
            }
        }


      
        

    }

    public function checkForKhaadiDuplicates()
    {
        $results = [];
        $orders = Shipment::where('created_at','>',Carbon::now()->subMinutes(10)->toDateTimeString())
                            ->whereNull('type')
                            ->whereSellerId(119)
                            ->pluck('order_id');

        foreach ($orders as $key => $order) {
            
            if (Shipment::whereOrderId($order)->whereNull('type')->count() > 1) {
                $shipments = Shipment::whereOrderId($order)->get(['tracking_number', 'status', 'created_at', 'order_id', 'id']);
                $results[] = $shipments;
            }
        }

        Log::info('check for duplicates');

        if (count($results)) {
            $message = count($results).' khaadi orders shipments are generated more than 1 times | '.json_encode($results);
            Mail::raw($message, function ($m)  {
                $m->to(['<EMAIL>'])->subject('KHAADI Per Order Duplicate Shipments');
            });
        }
    }

    public function checkForNotBookedLocationOrders()
    {

        // $orders = Order::whereSellerId(119)
        //     ->where('seller_location_id','!=',1)
        //     ->whereStatus('Pending')
        //     ->whereDate('created_date','>','2021-11-05')
        //     ->where('seller_location_assigned_date','<', Carbon::now()->subMinutes(3)->toDateTimeString())
        //     ->get(['id','marketplace_reference_id','created_date','seller_location_assigned_date']);

        $orders = DB::connection('mysql2')->select("SELECT o.id,o.marketplace_reference_id,o.created_date,o.seller_location_assigned_date
        FROM orders o 
        LEFT OUTER JOIN shipments s
          ON (o.id=s.order_id) 
          WHERE o.seller_id=119 and s.order_id IS NULL and o.status = 'Pending' and o.seller_location_id != 1
          and o.seller_location_assigned_date < '".Carbon::now()->subMinutes(3)->toDateTimeString()."'");
            
        $total_orders = count($orders);
        if ($total_orders) {
            $html = '<h2><b>'.$total_orders.' orders shipments are not generated </b></h2>'. self::jsonToDebug($orders);

            Mail::send([], [], function (Message $message) use ($html) {
                $message->to(['<EMAIL>','<EMAIL>'])
                ->subject('Khaadi Not Booked Location Orders')
                ->setBody($html, 'text/html');
            });
        }
        return $total_orders;
    } 

    public function checkForUncommitedQtyGreaterThanQty($seller_id)
    {

        $seller_ffc_inventories = DB::connection('mysql2')->select("select * from seller_ffc_inventories where seller_id = ".$seller_id." and uncommitted_qty > qty");
            
        $total_seller_ffc_inventories = count($seller_ffc_inventories);
        if ($total_seller_ffc_inventories) {
            $html = '<h2><b>'.$total_seller_ffc_inventories.' rows found where uncommited qty is greater than qty </b></h2>'. self::jsonToDebug($seller_ffc_inventories);

            Mail::send([], [], function (Message $message) use ($html,$seller_id) {
                $message->to(['<EMAIL>'])
                ->subject('Un commited qty - Greater than - Quantity <'.$seller_id.'>')
                ->setBody($html, 'text/html');
            });
        }
        return $total_seller_ffc_inventories;
    } 

    public static function jsonToDebug($jsonText = '')
    {
        for($i=0;$i<count($jsonText);$i++){
            $jsonText[$i] = (array)$jsonText[$i];
        }

        // $arr = json_decode($jsonText, true);
        // $html = "";
        // if ($arr && is_array($arr)) {
        //     $html .= self::_arrayToHtmlTableRecursive($arr);
        // }
        $html = self::_arrayToHtmlTableRecursive($jsonText);
        return $html;
    }

    private static function _arrayToHtmlTableRecursive($arr) {
        $str = "<table><tbody>";
        foreach ($arr as $key => $val) {
            $str .= "<tr>";
            $str .= "<td>$key</td>";
            $str .= "<td>";
            if (is_array($val)) {
                if (!empty($val)) {
                    $str .= self::_arrayToHtmlTableRecursive($val);
                }
            } else {
                $str .= "<strong>$val</strong>";
            }
            $str .= "</td></tr>";
        }
        $str .= "</tbody></table>";

        return $str;
    }


    public function checkForUniversalShipmentExceptions($hours)
    {
        Log::info('check for universal shipment exception STARTED');

        $last_check_time = Carbon::now()->subHours($hours)->toDateTimeString();

        $exceptions = DB::connection('mysql2')
            ->table('exceptions')
            ->where('exceptions.created_at', '>', $last_check_time)
            ->where('exceptions.entity_name', 'Shipment')
            ->whereIn('exceptions.type_id', [12, 13, 14] )
            ->join('shipment_extras', 'exceptions.entity_id', '=', 'shipment_extras.shipment_id')
            ->where('shipment_extras.value','universal')
            ->join('sellers', 'exceptions.seller_id', '=', 'sellers.id')
            ->select(
                'exceptions.seller_id as seller_id',
                'exceptions.entity_id as shipment_id',
                'exceptions.entity_reference_id as tracking_number',
                'exceptions.external_entity_id as courier',
                'exceptions.type_id as type_id',
                'exceptions.message as message',
                'exceptions.detail as detail',
                'sellers.full_name as seller_name',
                'sellers.company_name as seller_company_name',
                'sellers.email as seller_email',
                'shipment_extras.value as type')
            ->get();


        if ($exceptions->isNotEmpty()) {

            $file = new Filesystem;
            $file->cleanDirectory(storage_path('app/public').'/temp-files/');
            $filePath = storage_path('app/public').'/temp-files/universal-shipment-exceptions.csv';
            $output = fopen($filePath,'w') or die("Can't open php://output");
            fputcsv($output, array( 'Seller ID', 'Seller Name', 'Seller Company Name', 'Seller Email', 'Shipment ID', 'Tracking Number', 'Courier', 'Message', 'Detail'));


            foreach ($exceptions as $exception) {
                
                fputcsv($output, [
                    $exception->seller_id,
                    $exception->seller_name,
                    $exception->seller_company_name,
                    $exception->seller_email,
                    $exception->shipment_id,
                    $exception->tracking_number,
                    config('unityexception.swap_external_entity_id')[$exception->courier],
                    $exception->message,
                    $exception->detail
                ]);
            }

            fclose($output) or die("Can't close php://output");

            $message = 'Universal Shipment Exceptions Report, files attached, it gets exceptions generated from '.$last_check_time.' till now';

            Mail::raw($message, function ($m) use ($filePath)  {
                $m->to(['<EMAIL>'])
                ->bcc(['<EMAIL>'])
                ->subject('Universal Shipment Exceptions Report');
                $m->attach($filePath);
            });

            Log::info('check for universal shipment exception | Email send');
        
        } else {
            Log::info('check for universal shipment exception | no exception found');
        }
        Log::info('check for universal shipment exception | ENDED');
    }

    public function compareJdotShipmentCodAndOrdersCod()
    {
        
        $shipment_ids = Shipment::whereSellerId(4950)
                            ->where('created_at','>',Carbon::now()->subMinutes(5)->toDateTimeString())
                            ->where('cod', '>', 0)
                            ->whereNull('type')
                            ->where('status','!=','Cancelled')
                            ->whereHas('order', function ($query) {
                                $query->whereColumn('shipments.cod', '!=', 'orders.grand_total');
                            })
                            ->pluck('id');
        
        if(count($shipment_ids) > 0){
            Log::info('Some shipments found');
            $shipment_ids_as_string = $shipment_ids->implode(',');
            Log::info($shipment_ids_as_string);

            $message = ' Jdot orders and shipments are not matching for these list of shipments ids | '.$shipment_ids_as_string;
            Mail::raw($message, function ($m)  {
                $m->to(['<EMAIL>'])->subject('JDOT COD amount of certains shipments are not matching betwen shipment and orders');
            });
        }else{
            Log::info('None such cases found');

        }



    }
}
