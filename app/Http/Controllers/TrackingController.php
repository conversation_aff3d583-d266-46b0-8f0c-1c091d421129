<?php

namespace App\Http\Controllers;

use App\Events\ShipmentTrackingEvent;
use App\Models\Courier\SellerCourierMNP;
use App\Models\Courier\SellerCourierRider;
use App\Models\Courier\SellerCourierTraxNew;
use App\Models\Order;
use App\Models\OrderComments;
use App\Models\Shipment;
use App\Models\ShipmentCourierHistory;
use App\Traits\TrackingTrait;
use Illuminate\Http\Request;

class TrackingController extends Controller
{
    use TrackingTrait;
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $title = 'Tracking';
        return view('tracking.home',compact('title'));
    }

    public function trackOrder(Request $request)
    {
        $shipment = Shipment::whereTrackingNumber($request->tracking_number)->firstOrFail();
        $tracking_data = $this->trackSingle($shipment,  config('enum.tracking_types')['ONLY-RESPONSE']);

        $unity_data = ShipmentCourierHistory::whereShipmentId($shipment->id)->orderBy('id','desc')->get();
        $sync_data = OrderComments::whereOrderId($shipment->order_id)->where('order_comments.key','Shipment Tracking Storefront Synced Process')->orderBy('id','desc')->get();

        // return  $tracking_data;
        $title = 'Shipment #'.$request->tracking_number;
        return view('tracking.home',compact('tracking_data', 'unity_data', 'sync_data', 'title'));
    }

    public function forceTrackingPage()
    {
        $title = 'Force Track';
        return view('force-track.home',compact('title'));
    }

    public function forceTrackingRun(Request $request)
    {
        // return $request->orders;
        \Log::info("forceTrackingRun list of orders >>> ".$request->orders);
        $issues = '';
        $errors = 0;
        $orders_array = explode(',',$request->orders);
        foreach($orders_array as $item){
            $order = Order::whereMarketplaceReferenceId($item);
            if($order->exists()){
                $order = $order->value('id');
                $shipment = Shipment::whereOrderId($order);
                if($shipment->exists()){
                    $shipment = $shipment->first();
                    event(new ShipmentTrackingEvent($shipment));
                }
                else{
                    $issues .= $item.'|';
                    $errors++;
                }
            }
            else{
                $issues .= $item.'|';
                $errors++;
            }
        }

        if(count($orders_array) == $errors){
            return redirect()->back()->with('error','No order processed');
        }
        elseif(count($orders_array) != $errors && $errors > 0){
            return redirect()->back()->with('warning','Some Orders processed except | '.$issues);
        }
        else{
            return redirect()->back()->with('success','Process completed successfully');
        }
    }
}
