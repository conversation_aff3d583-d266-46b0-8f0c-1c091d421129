<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\City;
use App\Models\Courier\CourierCity;
use App\Models\Courier\Courier;
use App\Models\StateOrProvince;
use Yajra\DataTables\DataTables;

class CourierCityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        
        $title = 'Courier City Mapping';
        $couriers = Courier::whereStatus('1')->get();
        // $states = StateOrProvince::all();
        // $cities = City::all();
        return view('courier-city.home',compact('title','couriers'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'courier' => 'required',
            'master_city' => 'required',
            'courier_city_code' => 'required',
            'courier_city_name' => 'required'
        ]);

        $courier_city = new CourierCity;
        $courier_city->setConnection('mysql3');
        $courier_city->courier_id = $request->courier;
        $courier_city->city_id = $request->master_city;
        $courier_city->courier_city_code = $request->courier_city_code;
        $courier_city->courier_city_name = $request->courier_city_name;
        $courier_city->save();

        return redirect()->back()->with('success','Successfully Added Courier City.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // return $request->all();
        $request->validate([
            'edit_courier' => 'required',
            'edit_master_city' => 'required',
            'edit_courier_city_code' => 'required',
            'edit_courier_city_name' => 'required'
        ]);

        $courier_city = CourierCity::where('id',$id)->first();
        $courier_city->setConnection('mysql3');
        $courier_city->courier_id = $request->edit_courier;
        $courier_city->city_id = $request->edit_master_city;
        $courier_city->courier_city_code = $request->edit_courier_city_code;
        $courier_city->courier_city_name = $request->edit_courier_city_name;
        $courier_city->save();

        return redirect()->back()->with('success','Successfully Updated Courier City.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $courier_city = CourierCity::where('id',$id)->first();
        $courier_city->delete();

        return redirect()->back()->with('success','Successfully Deleted Courier City: '.$courier_city->courier_city_name);
    }

    public function allCourierCities()
    {
        // return $request->all();
        // return CourierCity::with(['courier','city'])->get();
        
		return DataTables::of(CourierCity::with(['city','courier'])->selectRaw('courier_cities.*'))->setRowId('id')->make(true);
		
    }
}
