<?php

namespace App\Http\Controllers;

use App\Events\AmazonShippingTrackingtEvent;
use App\Events\DPDTrackingtEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

use App\Models\Shipment;
use App\Models\ShipmentTrackingJob;
use App\Models\ShipmentHistory;

use App\Events\BlueExPaymentEvent;
use App\Events\BlueExTrackingEvent;
use App\Events\CallCourierPaymentEvent;
use App\Events\CallCourierTrackingEvent;
use App\Events\DaewooTrackingEvent;
use App\Events\ShipmentTrackingEvent;
use App\Events\KhaadiShipmentTrackingEvent;
use App\Events\LcsMerchantPaymentEvent;
use App\Events\LCSMerchantTrackingEvent;
use App\Events\RiderPaymentEvent;
use App\Events\LcsPaymentEvent;
use App\Events\LCSTrackingEvent;
use App\Events\MnPPaymentEvent;
use App\Events\MnpTrackingEvent;
use App\Events\MoveXPaymentEvent;
use App\Events\PostExPaymentEvent;
use App\Events\PostExTrackingEvent;
use App\Events\StallionPaymentEvent;
use App\Events\TCSPaymentEvent;
use App\Events\TCSTrackingEvent;
use App\Events\TPLRiderTrackingEvent;
use App\Events\TraxPaymentEvent;
use App\Events\TraxTrackingEvent;
use App\Events\UniversalShipmentTrackingEvent;
use Illuminate\Support\Facades\Redis;
use App\Traits\TrackingTrait;

use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Queue;

class ShipmentController extends Controller
{
    private $client;
    use TrackingTrait;

    public function __construct() {
		$this->client = new Client();
    }

    public function tracking($enterprise = 0, $universal = false , $courier_id = 0) {
        
        $terminal_status = [config('enum.shipment_status')['DELIVERED'], config('enum.shipment_status')['CANCELLED'], config('enum.shipment_status')['RETURNED'], config('enum.shipment_status')['LOST'] , config('enum.shipment_status')['RETURNED_RECEIVED']];
        $ignore_couriers = [1,2,3,5,6,9,15,20,25,35,13,10,7,11,4,28,19,8,34];

        if($courier_id == 0){
            if($universal){ $ignore_couriers = [1,2,3,6,9,15,20,25]; }
            $all_shipments = Shipment::whereNotIn('status', $terminal_status)->whereNotIn('courier_id', $ignore_couriers);
        }else{
            $all_shipments = Shipment::whereNotIn('status', $terminal_status)->where('courier_id', $courier_id);
        }


        if (!$enterprise && $universal) {                       // For NON-Khaadi Universal
            $all_shipments = $all_shipments->whereHas('shipment_booking_courier', function($q) {
                $q->where('value', 'universal');
            });
        } elseif (!$enterprise && !$universal) {
            $all_shipments = $all_shipments->whereHas('shipment_booking_courier', function($q) {
                $q->where('value', 0);
            });
        }

        if ($enterprise == 0) {
            $all_shipments = $all_shipments->whereNotIn('seller_id', [119, 120]);
        } elseif ($enterprise == 1) {
            $all_shipments = $all_shipments->whereIn('seller_id', [119, 120]);
        } else {
            return 'Enterprise not found';
        }

        $all_shipments = $all_shipments->whereHas('seller', function ($query) {
            $query->where('activated', 2);
        });

        $all_shipments = $all_shipments->orderBy('id', 'desc')->pluck('id');

        if ($all_shipments->isNotEmpty()) {
            foreach ($all_shipments->chunk(10) as $shipments) {
                self::generateQueue($shipments, $enterprise, 1, $universal , $courier_id);
            }
        } else {
            return "No shipments found for tracking";
        }

        // temp work for Amazon Shipping to prevent 429
        // if (Redis::connection()->llen('queues:khaadiTrackingAmazonShipping') > 0) {
         if (Queue::size('khaadiTrackingAmazonShipping') > 0) {

        } else {
            // $amazon_shippings = Shipment::whereNotIn('status', $terminal_status)->where('courier_id', 25)->where('seller_id', 120)->orderBy('id', 'desc')->pluck('id');
     
            // if ($amazon_shippings->isNotEmpty()) {
            //     foreach ($amazon_shippings->chunk(1) as $amazon_shipping) {
            //         //event(new AmazonShippingTrackingtEvent($amazon_shipping, 1));
            //     }
            // } else{
            //     Log::info("No shipments found for tracking Amazon Shipping");
            // }
        }
        

        Log::info('Tracking Completed');
        return "Tracking Completed";
    }

    public function jobs() {
        Log::info('Overall Tracking Started');
        return $this->tracking();
    }

    public function specificJobs($courier_id) {
        Log::info('Specific Courier Tracking Started');
        return $this->tracking(0,false,$courier_id);
    }

    public function khaadi_jobs() {
        Log::info('Khaadi Tracking Started');
        
        if (Queue::size('khaadiTracking')) {
            return "full";
        } else {
            return $this->tracking(1);
        }
    }

    public function universal_tracking() {
        Log::info('Universal Tracking Started');
        return $this->tracking(0, true);
    }

    public function track_payment($courier_id = 11) {
      
        Log::info('Payment Tracking Process is Started | Courier ID : '.$courier_id);

        $today = Carbon::now()->toDateString();
        
        $from_date = '2021-04-30';
        if ($courier_id == 28) {
            $from_date = '2024-01-06';
        }

        $shipments = Shipment::where('courier_id', $courier_id)
                    ->whereBetween('delivered_at',[$from_date,$today])
                    ->where('status', config('enum.shipment_status')['DELIVERED'])
                    ->where('cod_received',0)
                    ->whereHas('shipment_booking_courier', function($q){
                        $q->where('value', '0');
                     })
                    ->where('cod','>=',1)
                    ->pluck('id');

        if ($shipments->isNotEmpty()) {
            foreach ($shipments->chunk(10) as $shipment) {
                
                if ($courier_id == 4) {
                    event(new MnPPaymentEvent($shipment));
                } elseif ($courier_id == 5) {
                    event(new LcsPaymentEvent($shipment));
                } elseif ($courier_id == 7) {
                    // dd('df');
                    event(new CallCourierPaymentEvent($shipment));
                } elseif ($courier_id == 8) {
                    event(new TraxPaymentEvent($shipment));
                } elseif ($courier_id == 10) {
                    event(new BlueExPaymentEvent($shipment));
                } elseif ($courier_id == 11) {
                    event(new RiderPaymentEvent($shipment));
                } elseif ($courier_id == 13) {
                    event(new TCSPaymentEvent($shipment));
                } elseif ($courier_id == 19) {
                    event(new MoveXPaymentEvent($shipment));
                } elseif ($courier_id == 29) {
                    event(new StallionPaymentEvent($shipment));
                } elseif ($courier_id == 28) {
                    event(new PostExPaymentEvent($shipment));
                } elseif ($courier_id == 35) {
                    event(new LcsMerchantPaymentEvent($shipment));
                }else {
                    event(new MnPPaymentEvent($shipment));
                }
            }
        }

        Log::info('Payment Tracking Process is Ended | Count : '.$shipments->count().' | Courier ID : '.$courier_id);
        return 'Payment Tracking Process is Ended | Count : '.$shipments->count().' | Courier ID : '.$courier_id;
    }

    public function index()
    {
        // return config('unityexception.exception_types')['FALSE-RESPONSE'];
        $shipment = Shipment::find(138836);
        $track = $this->track2($shipment);
        // dd($shipment);
        // $mnp_instance = new SellerCourierMNP(true);
        // $track = $mnp_instance->track($shipment);
        dd($track);
        // dd('out of track');
        return $track;
    }
    public function forceTrack(Request $request)
    {
        try {

            $error = 1;
            
            if ($request->shipment_id) {
                // $terminal_status = [config('enum.shipment_status')['DELIVERED'], config('enum.shipment_status')['CANCELLED'], config('enum.shipment_status')['RETURNED'] , config('enum.shipment_status')['LOST']];
                $terminal_status = [];
                $ignore_couriers = [1,2,3,6,9,15,20];
                $shipment = Shipment::findOrFail($request->shipment_id);
                
                // if (in_array($shipment->status, $terminal_status)) {

                    if (!in_array($shipment->courier_id, $ignore_couriers)) {
                        self::generateQueue([$shipment->id], (in_array($shipment->seller_id,[119,120]) ? 1 : 0), config('enum.tracking_types')['FORCE-TRACK'], ($shipment->shipment_booking_courier->value == "universal" ? true : false) );
                        $error = 0;
                        $message = 'Shipment tracking is added in queue will be updated in a while.';
                    } else {
                        $message = 'Force track is not available for this shipment courier';
                    }
                    
                // } else {
                    // $message = 'Force track is only for terminal shipments';
                // }
            } else {
                $message = 'Shipment ID Not Found';
            }

        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        return response()->json(compact('error', 'message'), ( $error ? 406 : 200) );
    }

    private static function generateQueue($shipment, $enterprise = 0, $type = 1, $universal = false ,$courier_id = 0)
    {
        Log::info("generateQueue :::: 1");
        Log::info($shipment);
        Log::info("generateQueue :: ::: 2");
        Log::info($type);
        Log::info("generateQueue :: Courier ID :: ".$courier_id);
        Log::info("generateQueue :: enterprise :: ".$enterprise);
        Log::info("generateQueue :: universal :: ".$universal);

        if ($universal) {
            event(new UniversalShipmentTrackingEvent($shipment, $type));
        } else {

            if ($enterprise == 0) {
                Log::info("generateQueue :: ::: ShipmentTrackingEvent 4");
                if($courier_id == 5){
                    event(new LCSTrackingEvent($shipment, $type));
                }if($courier_id == 35){
                    event(new LCSMerchantTrackingEvent($shipment, $type));
                }if($courier_id == 13){
                    event(new TCSTrackingEvent($shipment, $type));
                }if($courier_id == 10){
                    event(new BlueExTrackingEvent($shipment, $type));
                }if($courier_id == 7){
                    event(new CallCourierTrackingEvent($shipment, $type));
                }if($courier_id == 11){
                    event(new TPLRiderTrackingEvent($shipment, $type));
                }if($courier_id == 4){
                    event(new MnpTrackingEvent($shipment, $type));
                }if($courier_id == 28){
                    event(new PostExTrackingEvent($shipment, $type));
                }if($courier_id == 8){
                    event(new TraxTrackingEvent($shipment, $type));
                }if($courier_id == 34){
                    event(new DaewooTrackingEvent($shipment, $type));
                }if($courier_id == 0){
                    event(new ShipmentTrackingEvent($shipment, $type));
                }
            } elseif ($enterprise == 1) {
                event(new KhaadiShipmentTrackingEvent($shipment, $type));
            }
        }
    }

    public function track_terminal()
    {
        $terminal_status = [config('enum.shipment_status')['DELIVERED'], config('enum.shipment_status')['CANCELLED'], config('enum.shipment_status')['RETURNED'], config('enum.shipment_status')['LOST']];
        $result_array = [];



        /// 1 DAY RE TRACK
        $today = Carbon::today();
        $all_shipments = ShipmentHistory::on('mysql2')
                    ->whereDate('created_at',$today->subDays(1)->toDateString())
                    ->whereIn('status',$terminal_status)
                    ->pluck('shipment_id');

        if ($all_shipments->isNotEmpty()) {
            foreach ($all_shipments->chunk(10) as $shipments) {
                self::generateQueue($shipments, 0, config('enum.tracking_types')['1DAY-RE-TRACK']);
            }
            $message = "1 Day Re Track ".$all_shipments->count()." shipments added";
        } else {
            $message = "1 Day Re Track  shipments found for tracking";
        }
        array_push($result_array, $message);
        Log::info($message);



        /// 3 DAY RE TRACK
        $today = Carbon::today();
        $all_shipments = ShipmentHistory::on('mysql2')
                    ->whereDate('created_at',$today->subDays(3)->toDateString())
                    ->whereIn('status',$terminal_status)
                    ->pluck('shipment_id');

        if ($all_shipments->isNotEmpty()) {
            foreach ($all_shipments->chunk(10) as $shipments) {
                self::generateQueue($shipments, 0, config('enum.tracking_types')['3DAY-RE-TRACK']);
            }
            $message = "3 Day Re Track ".$all_shipments->count()." shipments added";
        } else {
            $message = "3 Day Re Track No shipments found for tracking";
        }
        array_push($result_array, $message);
        Log::info($message);



        /// 7 DAY RE TRACK
        $today = Carbon::today();
        $all_shipments = ShipmentHistory::on('mysql2')
                    ->whereDate('created_at',$today->subDays(7)->toDateString())
                    ->whereIn('status',$terminal_status)
                    ->pluck('shipment_id');

        if ($all_shipments->isNotEmpty()) {
            foreach ($all_shipments->chunk(10) as $shipments) {
                self::generateQueue($shipments, 0, config('enum.tracking_types')['7DAY-RE-TRACK']);
            }
            $message = "7 Day Re Track ".$all_shipments->count()." shipments added";
        } else {
            $message = "7 Day Re Track No shipments found for tracking";
        }
        array_push($result_array, $message);
        Log::info($message);

        return $result_array;
    }

    // public function FunctionName()
    // {
    //     $shipment = Shipment::find(139933);
    //     event(new ShipmentTrackingEvent($shipment, 1));
    // }



    public function forceTrackMultiple(Request $request)
    {
        $error = 1;
        $message = [];
        
        try {

            if ($request->shipment_ids) {

                $ignore_couriers = [1,2,3,6,9,15,20];
                $shipments = Shipment::whereIn('id', explode(',', $request->shipment_ids) )->with('shipment_booking_courier')->get();

                foreach ($shipments as $shipment) {
                    
                    if (!in_array($shipment->courier_id, $ignore_couriers)) {
                        self::generateQueue([$shipment->id], (in_array($shipment->seller_id,[119,120]) ? 1 : 0), config('enum.tracking_types')['FORCE-TRACK'], ($shipment->shipment_booking_courier->value == "universal" ? true : false) );
                        $error = 0;
                        $message[$shipment->tracking_number] = 'Shipment tracking is added in queue will be updated in a while';
                    } else {
                        $message[$shipment->tracking_number] = 'Force track is not available for this shipment courier';
                    }
                }

            } else {
                $message = 'Shipment ID\'s Not Found';
            }

        } catch (Exception $e) {
            $error = 1;
            $message['error'] = $e->getMessage();
        }

        return response()->json(compact('error', 'message'), ( $error ? 406 : 200) );
    }
}








