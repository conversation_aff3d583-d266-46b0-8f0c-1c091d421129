<?php

namespace App\Http\Controllers;

use App\Models\City;
use App\Models\CourierCity;
use App\Models\StateOrProvince;

use Illuminate\Http\Request;

class CityController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $title = 'City Mapping';
        $states = StateOrProvince::all();
        $country_codes = City::selectRaw('count(id),country_code')->groupBy('country_code')->get();
        return view('city.home',compact('title','states','country_codes'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'city_name' => 'required',
            'state_province' => 'required',
            'country_code' => 'required',
        ]);

        $city = new City;
        $city->name = $request->city_name;
        $city->state_or_province_id = $request->state_province;
        $city->country_code = $request->country_code;
        $city->save();

        $courier_city = new CourierCity;
        $courier_city->setConnection('mysql3');
        $courier_city->courier_id = 1;
        $courier_city->city_id = $city->id;
        $courier_city->courier_city_code = $request->city_name;
        $courier_city->courier_city_name = $request->city_name;
        $courier_city->save();

        return redirect()->back()->with('success','Successfully Added City.');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // return $request->all();
        $request->validate([
            'city_name' => 'required',
            'edit_state_province' => 'required',
            'edit_country_code' => 'required',
        ]);

        $city = City::where('id',$id)->first();
        $city->name = $request->city_name;
        $city->state_or_province_id = $request->edit_state_province;
        $city->country_code = $request->edit_country_code;
        $city->save();

        return redirect()->back()->with('success','Successfully Updated City.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $city = City::where('id',$id)->first();
        $city->delete();

        return redirect()->back()->with('success','Successfully Deleted City: '.$city->name);
    }

    public function allCities(Request $request)
    {
        // return $request->all();
        return City::where('country_code','PK')->with('state_or_province')->get();
    }

    public function search()
    {
        return City::where('name', 'LIKE', "{$_GET['string']}%")->select('id','name','country_code')->get();
    }
}
