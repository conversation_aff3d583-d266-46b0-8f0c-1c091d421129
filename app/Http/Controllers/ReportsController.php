<?php

namespace App\Http\Controllers;

use App\Models\Shipment;
use App\Models\OrderComments;
use App\Models\Order;
use App\Models\ShipmentCourierHistory;
use App\Models\Courier\Courier;
use App\Models\Exception;
use Carbon\Carbon;
use Exception as GlobalException;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class ReportsController extends Controller
{
    public function syncReportHome()
    {
        // return OrderComments::where('key','Shipment Tracking Storefront Synced Process')->get();
        $title = 'Khaadi Sync Report';
        return view('reports.sync-report',compact('title'));
    }

    public function syncReportShow($id)
    {
        // return $request->all();
        // return 'yo';
        // $o_id = Order::where('marketplace_reference_id',$id)->first();
        // return Shipment::where('order_id',$o_id)->first();
        
  
        $data = [[]];
        $ids = explode(',',$id);
        $o_id;
        $s_id;
        // return $ids;
        $i = 0;
        foreach($ids as $id){
            $o_id = \DB::connection("mysql2read")->table("orders")->where('marketplace_reference_id',$id)->where('seller_id','119')->first();
            if(!$o_id){

            }
            else{
                $s_id = \DB::connection("mysql2read")->table("shipments")->where('order_id',$o_id->id)->first();
                if(!$s_id){

                }
                else{
                    $data[$i]['order_id'] = $o_id->marketplace_reference_id;
                    $data[$i]['tracking_id'] = $s_id->tracking_number;
                    $data[$i]['courier_name'] = \DB::connection("mysql2read")->table("couriers")->where('id',$s_id->courier_id)->value('name');
                    $data[$i]['last_run'] = $s_id->last_run;
                    $data[$i]['last_tracking_status'] = \DB::connection("mysql2read")->table("shipment_courier_histories")->where('shipment_id',$s_id->id)->orderBy('status_at','desc')->take(1)->value('status');
                    $data[$i]['last_magento_status'] = \DB::connection("mysql2read")->table("order_comments")->where('order_id',$o_id->id)->where('key','Shipment Tracking Storefront Synced Process')->where('status','Success')->orderBy('created_at','desc')->take(1)->value('value');
                    $data[$i]['last_magento_status_date'] = \DB::connection("mysql2read")->table("order_comments")->where('order_id',$o_id->id)->where('key','Shipment Tracking Storefront Synced Process')->where('status','Success')->orderBy('created_at','desc')->take(1)->value('created_at');
                }
                
            }
            

            $o_id;
            $s_id;
            $i++;
        }

        return $data;
        // return Shipment::where('shipments.seller_id','119')
        // ->whereIn('orders.marketplace_reference_id',$ids)
        // ->join('orders','shipments.order_id','orders.id')
        // ->join('couriers','shipments.courier_id','couriers.id')
        // ->join('shipment_courier_histories','shipments.id','shipment_courier_histories.shipment_id')
        // ->join('order_comments','orders.id','order_comments.order_id')
        // ->where('order_comments.key','Shipment Tracking Storefront Synced Process')
        // ->where('order_comments.status','Success')
        // ->selectRaw('orders.marketplace_reference_id as order_id, shipments.tracking_number as tracking_id, couriers.name as courier_name, shipments.last_run as last_run,shipment_courier_histories.status as last_tracking_status,order_comments.value as last_magento_status')
        // ->orderBy('shipment_courier_histories.status_at','desc')
        // ->get();
        
       

            
       
    }

    public function courierException()
    {
        $yesterday = Carbon::now()->subDay()->toDateString();
        $exceptions = Exception::on('mysql2')
                    ->whereSellerId(119)
                    ->whereExternalEntityName( config('unityexception.external_entity_name')['COURIER'] )
                    ->whereEntityName( 'Shipment' )
                    ->whereDate('created_at', $yesterday)
                    ->with(['type','shipment','shipment.orderForReports'])
                    ->orderBy('external_entity_id')
                    ->get(['entity_reference_id','message','external_entity_id','type_id','entity_id']);

        if ($exceptions) {

            $file = new Filesystem;
			$file->cleanDirectory(storage_path('app/public').'/temp-files/');

            $courier_id = null;
            $output = null;
            $files = [];

            
            try {

                foreach ($exceptions as $key => $value) {


                    if (!$key) {
                        $courier_id = $value->external_entity_id;
                        $filePath = storage_path('app/public').'/temp-files/'.config('unityexception.swap_external_entity_id')[$courier_id].'-courier-exception.csv';
                        $files[] = $filePath;
                        $output = fopen($filePath,'w') or die("Can't open php://output");
                        fputcsv($output, array( 'CN (tracking number)','Order No','Message','Type','Type Detail'));
                    } else {

                        if ($courier_id != $value->external_entity_id) {
                            fclose($output) or die("Can't close php://output");
                            $courier_id = $value->external_entity_id;
                            $filePath = storage_path('app/public').'/temp-files/'.config('unityexception.swap_external_entity_id')[$courier_id].'-courier-exception.csv';
                            $files[] = $filePath;
                            $output = fopen($filePath,'w') or die("Can't open php://output");
                            fputcsv($output, array( 'CN (tracking number)','Order No','Message','Type','Type Detail'));
                        }

                    }
                    fputcsv($output, [
                                        $value->entity_reference_id,
                                        isset($value->shipment->orderForReports->marketplace_reference_id) ? $value->shipment->orderForReports->marketplace_reference_id : '-',
                                        $value->message,
                                        isset($value->type->name) ? $value->type->name : '-',
                                        isset($value->type->detail) ? $value->type->detail : '-'
                    ]);
                }
                fclose($output) or die("Can't close php://output");
            } catch (GlobalException $e) {
                Log::info($e->getMessage());
                Log::info($e->getTraceAsString());
            }

            if (count($files) > 0) {

                $unity = explode(',',env('UNITY_POC'));
                $others = explode(',',env('KHAADI_POC'));


                Mail::raw('Daily Exceptions Report, All files attached', function ($m) use ($yesterday,$others,$unity, $files)  {
                    $m->to($others)
                    ->bcc($unity)
                    ->subject('Khaadi Courier Exception Daily Report | '.$yesterday);
                    
                    foreach ($files as $key => $value) {
                        $m->attach($value);
                    }
                });
            }
            Log::info('Exception Count '.$exceptions->count());
        } else {
            Log::info("No exceptions");
        }
    }
}
