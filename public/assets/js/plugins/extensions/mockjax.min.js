(function(d){function q(a){void 0==window.DOMParser&&window.ActiveXObject&&(DOMParser=function(){},DOMParser.prototype.parseFromString=function(a){var b=new ActiveXObject("Microsoft.XMLDOM");b.async="false";b.loadXML(a);return b});try{var b=(new DOMParser).parseFromString(a,"text/xml");if(d.isXMLDoc(b)){if(1==d("parsererror",b).length)throw"Error: "+d(b).text();}else throw"Unable to parse XML";return b}catch(e){a=void 0==e.name?e:e.name+": "+e.message,d(document).trigger("xmlParseError",[a])}}function m(a,
b){var e=!0;if("string"===typeof b)return d.isFunction(a.test)?a.test(b):a==b;d.each(a,function(c){if(void 0===b[c])return e=!1;e="object"===typeof b[c]&&null!==b[c]?e&&m(a[c],b[c]):a[c]&&d.isFunction(a[c].test)?e&&a[c].test(b[c]):e&&a[c]==b[c]});return e}function r(a,b){if(d.isFunction(a))return a(b);if(d.isFunction(a.url.test)){if(!a.url.test(b.url))return null}else{var e=a.url.indexOf("*");if(a.url!==b.url&&-1===e||!(new RegExp(a.url.replace(/[-[\]{}()+?.,\\^$|#\s]/g,"\\$&").replace(/\*/g,".+"))).test(b.url))return null}return a.data&&
b.data&&!m(a.data,b.data)||a&&a.type&&a.type.toLowerCase()!=b.type.toLowerCase()?null:a}function s(a,b,e){var c=function(c){return function(){return function(){var c;this.status=a.status;this.statusText=a.statusText;this.readyState=4;d.isFunction(a.response)&&a.response(e);"json"==b.dataType&&"object"==typeof a.responseText?this.responseText=JSON.stringify(a.responseText):"xml"==b.dataType?"string"==typeof a.responseXML?(this.responseXML=q(a.responseXML),this.responseText=a.responseXML):this.responseXML=
a.responseXML:this.responseText=a.responseText;if("number"==typeof a.status||"string"==typeof a.status)this.status=a.status;"string"===typeof a.statusText&&(this.statusText=a.statusText);c=this.onreadystatechange||this.onload;d.isFunction(c)?(a.isTimeout&&(this.status=-1),c.call(this,a.isTimeout?"timeout":void 0)):a.isTimeout&&(this.status=-1)}.apply(c)}}(this);a.proxy?k({global:!1,url:a.proxy,type:a.proxyType,data:a.data,dataType:"script"===b.dataType?"text/plain":b.dataType,complete:function(b){a.responseXML=
b.responseXML;a.responseText=b.responseText;a.status===d.mockjaxSettings.status&&(a.status=b.status);a.statusText===d.mockjaxSettings.statusText&&(a.statusText=b.statusText);this.responseTimer=setTimeout(c,a.responseTime||0)}}):!1===b.async?c():this.responseTimer=setTimeout(c,a.responseTime||50)}function t(a,b,e,c){a=d.extend(!0,{},d.mockjaxSettings,a);"undefined"===typeof a.headers&&(a.headers={});a.contentType&&(a.headers["content-type"]=a.contentType);return{status:a.status,statusText:a.statusText,
readyState:1,open:function(){},send:function(){c.fired=!0;s.call(this,a,b,e)},abort:function(){clearTimeout(this.responseTimer)},setRequestHeader:function(b,c){a.headers[b]=c},getResponseHeader:function(b){if(a.headers&&a.headers[b])return a.headers[b];if("last-modified"==b.toLowerCase())return a.lastModified||(new Date).toString();if("etag"==b.toLowerCase())return a.etag||"";if("content-type"==b.toLowerCase())return a.contentType||"text/plain"},getAllResponseHeaders:function(){var b="";d.each(a.headers,
function(a,c){b+=a+": "+c+"\n"});return b}}}function u(a,b,e){"GET"===a.type.toUpperCase()?g.test(a.url)||(a.url+=(/\?/.test(a.url)?"&":"?")+(a.jsonp||"callback")+"=?"):a.data&&g.test(a.data)||(a.data=(a.data?a.data+"&":"")+(a.jsonp||"callback")+"=?");a.dataType="json";if(a.data&&g.test(a.data)||g.test(a.url)){v(a,b,e);var c=/^(\w+:)?\/\/([^\/?#]+)/.exec(a.url),c=c&&(c[1]&&c[1]!==location.protocol||c[2]!==location.host);a.dataType="script";if("GET"===a.type.toUpperCase()&&c){var c=e&&e.context||a,
f=null;b.response&&d.isFunction(b.response)?b.response(e):"object"===typeof b.responseText?d.globalEval("("+JSON.stringify(b.responseText)+")"):d.globalEval("("+b.responseText+")");n(a,c,b);p(a,c,b);d.Deferred&&(f=new d.Deferred,"object"==typeof b.responseText?f.resolveWith(c,[b.responseText]):f.resolveWith(c,[d.parseJSON(b.responseText)]));return(a=f)?a:!0}}return null}function v(a,b,d){var c=d&&d.context||a,f=a.jsonpCallback||"jsonp"+w++;a.data&&(a.data=(a.data+"").replace(g,"="+f+"$1"));a.url=
a.url.replace(g,"="+f+"$1");window[f]=window[f]||function(d){data=d;n(a,c,b);p(a,c,b);window[f]=void 0;try{delete window[f]}catch(e){}head&&head.removeChild(script)}}function n(a,b,e){a.success&&a.success.call(b,e.responseText||"",status,{});a.global&&(b=[{},a],(a.context?d(a.context):d.event).trigger("ajaxSuccess",b))}function p(a,b){a.complete&&a.complete.call(b,{},status);if(a.global){var e=[{},a];("ajaxComplete".context?d("ajaxComplete".context):d.event).trigger(e,void 0)}a.global&&!--d.active&&
d.event.trigger("ajaxStop")}function x(a,b){if(a.url instanceof RegExp&&a.hasOwnProperty("urlParams")){var d=a.url.exec(b.url);if(1!==d.length){d.shift();var c=0,f=Math.min(d.length,a.urlParams.length),h={};for(c;c<f;c++)h[a.urlParams[c]]=d[c];b.urlParams=h}}}var k=d.ajax,h=[],l=[],g=/=\?(&|$)/,w=(new Date).getTime();d.extend({ajax:function(a,b){var e,c,f;"object"===typeof a?(b=a,a=void 0):b.url=a;c=d.extend(!0,{},d.ajaxSettings,b);for(var g=0;g<h.length;g++)if(h[g]&&(f=r(h[g],c))){l.push(c);d.mockjaxSettings.log(f,
c);if("jsonp"===c.dataType&&(e=u(c,f,b)))return e;f.cache=c.cache;f.timeout=c.timeout;f.global=c.global;x(f,b);(function(a,b,c,f){e=k.call(d,d.extend(!0,{},c,{xhr:function(){return t(a,b,c,f)}}))})(f,c,b,h[g]);return e}if(!0===d.mockjaxSettings.throwUnmocked)throw"AJAX not mocked: "+b.url;return k.apply(d,[b])}});d.mockjaxSettings={log:function(a,b){if(!1!==a.logging&&("undefined"!==typeof a.logging||!1!==d.mockjaxSettings.logging)&&window.console&&console.log){var e="MOCK "+b.type.toUpperCase()+
": "+b.url,c=d.extend({},b);if("function"===typeof console.log)console.log(e,c);else try{console.log(e+" "+JSON.stringify(c))}catch(f){console.log(e)}}},logging:!0,status:200,statusText:"OK",responseTime:500,isTimeout:!1,throwUnmocked:!1,contentType:"text/plain",response:"",responseText:"",responseXML:"",proxy:"",proxyType:"GET",lastModified:null,etag:"",headers:{etag:"IJF@H#@923uf8023hFO@I#H#","content-type":"text/plain"}};d.mockjax=function(a){var b=h.length;h[b]=a;return b};d.mockjaxClear=function(a){1==
arguments.length?h[a]=null:h=[];l=[]};d.mockjax.handler=function(a){if(1==arguments.length)return h[a]};d.mockjax.mockedAjaxCalls=function(){return l}})(jQuery);
