!function(a,b,c,d){"use strict";var e="datepaginator",f=function(b,c){this._element=b,this.$element=a(b),this._init(c)};f.defaults={fillWidth:!0,highlightSelectedDate:!0,highlightToday:!0,hint:"dddd, Do MMMM YYYY",injectStyle:!0,itemWidth:35,navItemWidth:20,offDays:"Sat,Sun",offDaysFormat:"ddd",onSelectedDateChanged:null,selectedDate:moment().clone().startOf("day"),selectedDateFormat:"YYYY-MM-DD",selectedItemWidth:140,showCalendar:!0,showOffDays:!0,showStartOfWeek:!0,size:d,startOfWeek:"Mon",startOfWeekFormat:"ddd",squareEdges:!1,text:"ddd<br/>Do",textSelected:"dddd<br/>Do, MMMM YYYY",useBootstrap2:!1,width:0,startDate:moment(new Date(-864e13)),startDateFormat:"YYYY-MM-DD",endDate:moment(new Date(864e13)),endDateFormat:"YYYY-MM-DD"},f.prototype={setSelectedDate:function(a,b){this._setSelectedDate(moment(a,b?b:this.options.selectedDateFormat)),this._render()},remove:function(){this._destroy(),a.removeData(this,"plugin_"+e)},_init:function(b){this.options=a.extend({},f.defaults,b),this.options.width?this.options.fillWidth=!1:(this.options.width=this.$element.width(),this.options.fillWidth=!0),"string"==typeof this.options.startDate&&(this.options.startDate=moment(this.options.startDate,this.options.startDateFormat).clone().startOf("day")),"string"==typeof this.options.endDate&&(this.options.endDate=moment(this.options.endDate,this.options.endDateFormat).clone().startOf("day")),"string"==typeof this.options.selectedDate&&(this.options.selectedDate=moment(this.options.selectedDate,this.options.selectedDateFormat).clone().startOf("day")),this.options.selectedDate.isBefore(this.options.startDate)&&(this.options.selectedDate=this.options.startDate.clone()),this.options.selectedDate.isAfter(this.options.endDate)&&(this.options.selectedDate=this.options.endDate.clone()),"small"===this.options.size?this.options.size="sm":"large"===this.options.size&&(this.options.size="lg"),this._destroy(),this._subscribeEvents(),this._render()},_unsubscribeEvents:function(){this.$element.off("click"),this.$element.off("selectedDateChanged")},_subscribeEvents:function(){this._unsubscribeEvents(),this.$element.on("click",a.proxy(this._clickedHandler,this)),"function"==typeof this.options.onSelectedDateChanged&&this.$element.on("selectedDateChanged",this.options.onSelectedDateChanged),this.options.fillWidth&&a(b).on("resize",a.proxy(this._resize,this))||a(b).on("load",a.proxy(this._resize,this))},_destroy:function(){this.initialized&&(this.$calendar&&this.$calendar.datepicker("remove"),this.$wrapper.remove(),this.$wrapper=null,this._unsubscribeEvents()),this.initialized=!1},_clickedHandler:function(b){b.preventDefault();var c=a(b.target),d=c.attr("class");-1!=d.indexOf("dp-nav-left")?this._back():-1!=d.indexOf("dp-nav-right")?this._forward():-1!=d.indexOf("dp-item")&&this._select(c.attr("data-moment"))},_setSelectedDate:function(a){a.isSame(this.options.selectedDate)||a.isBefore(this.options.startDate)||a.isAfter(this.options.endDate)||(this.options.selectedDate=a.startOf("day"),this.$element.trigger("selectedDateChanged",[a.clone()]))},_back:function(){this._setSelectedDate(this.options.selectedDate.clone().subtract("day",1)),this._render()},_forward:function(){this._setSelectedDate(this.options.selectedDate.clone().add("day",1)),this._render()},_select:function(a){this._setSelectedDate(moment(a,this.options.selectedDateFormat)),this._render()},_calendarSelect:function(a){this._setSelectedDate(moment(a.date)),this._render()},_resize:function(){this.options.width=this.$element.width(),this._render()},_render:function(){var b=this;this.initialized?this.$calendar&&this.$calendar.datepicker("remove"):(this.$element.addClass(this.options.useBootstrap2?"pagination":"").removeClass("datepaginator datepaginator-sm datepaginator-lg").addClass("sm"===this.options.size?"datepaginator-sm":"lg"===this.options.size?"datepaginator-lg":"datepaginator"),this.$wrapper=a(this._template.list),this.$leftNav=a(this._template.navItem).addClass("dp-nav-left").addClass("sm"===this.options.size?"dp-nav-sm":"lg"===this.options.size?"dp-nav-lg":"").addClass(this.options.squareEdges?"dp-nav-square-edges":"").append(a(this._template.icon).addClass("icon-arrow-left32").addClass("dp-nav-left")).width(this.options.navItemWidth),this.$rightNav=a(this._template.navItem).addClass("dp-nav-right").addClass("sm"===this.options.size?"dp-nav-sm":"lg"===this.options.size?"dp-nav-lg":"").addClass(this.options.squareEdges?"dp-nav-square-edges":"").append(a(this._template.icon).addClass("icon-arrow-right32").addClass("dp-nav-right")).width(this.options.navItemWidth),this.$calendar=this.options.showCalendar?a(this._template.calendar):d,this._injectStyle(),this.initialized=!0);var c=this._buildData();this.$element.empty().append(this.$wrapper.empty()),this.$leftNav.removeClass("dp-no-select").attr("title",""),c.isSelectedStartDate&&this.$leftNav.addClass("dp-no-select").attr("title","Start of valid date range"),this.$wrapper.append(a(b._template.listItem).append(this.$leftNav)),a.each(c.items,function(c,d){var e=a(b._template.dateItem).attr("data-moment",d.m).attr("title",d.hint).width(d.itemWidth);d.isSelected&&b.options.highlightSelectedDate&&e.addClass("dp-selected"),d.isToday&&b.options.highlightToday&&e.addClass("dp-today"),d.isStartOfWeek&&b.options.showStartOfWeek&&e.addClass("dp-divider"),d.isOffDay&&b.options.showOffDays&&e.addClass("dp-off"),d.isSelected&&b.options.showCalendar&&e.append(b.$calendar),"sm"===b.options.size?e.addClass("dp-item-sm"):"lg"===b.options.size&&e.addClass("dp-item-lg"),d.isValid||e.addClass("dp-no-select"),e.append(d.text),b.$wrapper.append(a(b._template.listItem).append(e))}),this.$rightNav.removeClass("dp-no-select").attr("title",""),c.isSelectedEndDate&&this.$rightNav.addClass("dp-no-select").attr("title","End of valid date range"),this.$wrapper.append(a(b._template.listItem).append(this.$rightNav)),this.$calendar&&this.$calendar.datepicker({autoclose:!0,forceParse:!0,startView:0,minView:0,todayHighlight:!0,startDate:this.options.startDate.toDate(),endDate:this.options.endDate.toDate()}).datepicker("update",this.options.selectedDate.toDate()).on("changeDate",a.proxy(this._calendarSelect,this))},_injectStyle:function(){this.options.injectStyle&&!c.getElementById("bootstrap-datepaginator-style")&&a('<style type="text/css" id="bootstrap-datepaginator-style">'+this._css+"</style>").appendTo("head")},_buildData:function(){for(var a=this.options.width-(this.options.selectedItemWidth-this.options.itemWidth+2*this.options.navItemWidth),b=Math.floor(a/this.options.itemWidth),c=parseInt(b/2),d=Math.floor(a/b),e=Math.floor(this.options.selectedItemWidth+(a-b*d)),f=moment().startOf("day"),g=this.options.selectedDate.clone().subtract("days",c),h=this.options.selectedDate.clone().add("days",b-c),i={isSelectedStartDate:this.options.selectedDate.isSame(this.options.startDate)?!0:!1,isSelectedEndDate:this.options.selectedDate.isSame(this.options.endDate)?!0:!1,items:[]},j=g;j.isBefore(h);j.add("days",1)){var k=(j.isSame(this.options.startDate)||j.isAfter(this.options.startDate))&&(j.isSame(this.options.endDate)||j.isBefore(this.options.endDate))?!0:!1;i.items[i.items.length]={m:j.clone().format(this.options.selectedDateFormat),isValid:k,isSelected:j.isSame(this.options.selectedDate)?!0:!1,isToday:j.isSame(f)?!0:!1,isOffDay:-1!==this.options.offDays.split(",").indexOf(j.format(this.options.offDaysFormat))?!0:!1,isStartOfWeek:-1!==this.options.startOfWeek.split(",").indexOf(j.format(this.options.startOfWeekFormat))?!0:!1,text:j.isSame(this.options.selectedDate)?j.format(this.options.textSelected):j.format(this.options.text),hint:k?j.format(this.options.hint):"Invalid date",itemWidth:j.isSame(this.options.selectedDate)?e:d}}return i},_template:{list:'<ul class="pagination"></ul>',listItem:"<li></li>",navItem:'<a href="#" class="dp-nav"></a>',dateItem:'<a href="#" class="dp-item"></a>',icon:'<i class=""></i>',calendar:'<i id="dp-calendar" class="icon-calendar3"></i>'},_css:""};var g=function(a){b.console&&b.console.error(a)};a.fn[e]=function(b,c){return this.each(function(){var d=a.data(this,"plugin_"+e);"string"==typeof b?d?a.isFunction(d[b])&&"_"!==b.charAt(0)?("string"==typeof c&&(c=[c]),d[b].apply(d,c)):g("No such method : "+b):g("Not initialized, can not call method : "+b):d?d._init(b):a.data(this,"plugin_"+e,new f(this,b))})}}(jQuery,window,document);