<?php

use App\Helpers\TrackingRequest;
use App\Models\Order;
use Illuminate\Database\Seeder;

class TempSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tracking = new TrackingRequest();
        $result = ['count' => 0, 'success' => 0, 'failed' => 0, 'delivered' => 0, 'success_order_ids' => [], 'failed_order_ids' => [], 'results' => []];
        $orders = Order::find([275398,276870,276871,277045,278260,278415,278428,278514,278575,278830,279667,279675,279724,280030,280049,280089,280129,280165,280173,280203,280258,280260,280315,280332,280333,280340,280363,280387,280392,280399,280416,280438,280453,280479,280896,280897,280943,281053,281120,281195,281211,281273,281327,281328,281424,281431,281454,281495,281569,281636,290634,291288,292190,292222,292574,293654,295021,295034,295290,295524,295532,296479,296543,296577,296919,297080,297593,297839,297844,297852,297866,297894,297920,297952,297994,298027,298036,298074,298106,298132,298138,298210,298228,298520,298552,298555,299018,299022,299026,299064,299087,299094,299095,299099,299101,299111,299155,299183,299203,299263,299298,299314,299451,299462,299654,299775,299783,299785,299787,299795,299904,300427,300429,300445,300459,300577,300662,300751,300816,300967,301057,301300,301301,301912,301921,302133,302286,302422,302550,303005,303009,303028,303055,303471,304041,304110,304165,304230,304457,304468,304582,304680,304693,304700,304732,304767,305454,305467,305555,305645,305861,306186,306214,306769,306771,306874,306908,306922,306975,306998,307284,307410,308227,308231,308386,308388,308447,308470,308521,308542,308580,308586,308587,308617,308624,308639,308682,308697,308726,308752,309355,309552,309722,309873,313009,313053,313086,313087,313091,313094,313096,313131,318962,319213,321001,321581,322601,323763,323827,323828,323913,324840,327502,327807,328133,328393,328456,329698,329761,331180,331289,331290,331304,331306,331314,331347,331359,331471,331483,331486,331506,331528,331558,331562,331587,331600,331624,331629,331688,332127,332169,332183,332189,332194,332197,332202,332203,332205,332221,332235,332236,332238,332240,332252,332259,332288,332292,332295,332308,332341,332367,332374,332406,332407,332437,332440,332457,332458,332465,332486,332554,332569,332571,332594,332599,332631,332632,332645,332651,332653,332683,332710,332714,332762,332817,332822,332848,332855,332865,332876,332912,332998,333023,333073,333089,333091,333118,333132,333240,333274,333966,333970,334031,334074,334093,334123,334128,334149,334159,334164,334251,334269,334291,334340,334806,334960,336150,338605,357119,357916,357920,357923,357924,358356,358860,359955]);

        foreach ($orders as $order) {

            foreach ($order->shipments->sortByDesc('id')->take(1) as $shipment) {
                $temp_result = $tracking->track($shipment);  // calling tracking code and saving return
                $result['count']+=1;
                $result['results'][] = $temp_result;
                
                if ($temp_result['error']) {
                    $result['failed']+=1;
                    $result['failed_order_ids'][] = $order->id;
                } else {
                    $result['success']+=1;
                    $result['success_order_ids'][] = $order->id;

                    if ($temp_result['shipment_status'] == 'Delivered') {
                        $result['delivered']+=1;
                        
                        ///// Updating order item status
                        foreach ($shipment->items as $shipment_item) {
                            $shipment_item->order_item->update(['status' => config('enum.item_status')['COMPLETED']]);
                        }
                        ///// Updating order status
                        $order->update(['status' => config('enum.order_status')['COMPLETED']]);
                    }
                }
            }

        }
        dd($result);
    }
}
