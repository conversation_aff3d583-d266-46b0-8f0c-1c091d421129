<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Horizon Domain
    |--------------------------------------------------------------------------
    |
    | This is the subdomain where Horizon will be accessible from. If this
    | setting is null, Horizon will reside under the same domain as the
    | application. Otherwise, this value will serve as the subdomain.
    |
    */

    'domain' => null,

    /*
    |--------------------------------------------------------------------------
    | Horizon Path
    |--------------------------------------------------------------------------
    |
    | This is the URI path where Horizon will be accessible from. Feel free
    | to change this path to anything you like. Note that the URI will not
    | affect the paths of its internal API that aren't exposed to users.
    |
    */

    'path' => 'horizon',

    /*
    |--------------------------------------------------------------------------
    | Horizon Redis Connection
    |--------------------------------------------------------------------------
    |
    | This is the name of the Redis connection where Horizon will store the
    | meta information required for it to function. It includes the list
    | of supervisors, failed jobs, job metrics, and other information.
    |
    */

    'use' => 'default',

    /*
    |--------------------------------------------------------------------------
    | Horizon Redis Prefix
    |--------------------------------------------------------------------------
    |
    | This prefix will be used when storing all Horizon data in Redis. You
    | may modify the prefix when you are running multiple installations
    | of Horizon on the same server so that they don't have problems.
    |
    */

    'prefix' => env('HORIZON_PREFIX', 'horizon:'),

    /*
    |--------------------------------------------------------------------------
    | Horizon Route Middleware
    |--------------------------------------------------------------------------
    |
    | These middleware will get attached onto each Horizon route, giving you
    | the chance to add your own middleware to this list or change any of
    | the existing middleware. Or, you can simply stick with this list.
    |
    */

    'middleware' => ['web'],

    /*
    |--------------------------------------------------------------------------
    | Queue Wait Time Thresholds
    |--------------------------------------------------------------------------
    |
    | This option allows you to configure when the LongWaitDetected event
    | will be fired. Every connection / queue combination may have its
    | own, unique threshold (in seconds) before this event is fired.
    |
    */

    'waits' => [
        'redis:default' => 60,
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Trimming Times
    |--------------------------------------------------------------------------
    |
    | Here you can configure for how long (in minutes) you desire Horizon to
    | persist the recent and failed jobs. Typically, recent jobs are kept
    | for one hour while all failed jobs are stored for an entire week.
    |
    */

    'trim' => [
        'recent' => 60,
        'completed' => 60,
        'recent_failed' => 10080,
        'failed' => 10080,
        'monitored' => 10080,
    ],

    /*
    |--------------------------------------------------------------------------
    | Fast Termination
    |--------------------------------------------------------------------------
    |
    | When this option is enabled, Horizon's "terminate" command will not
    | wait on all of the workers to terminate unless the --wait option
    | is provided. Fast termination can shorten deployment delay by
    | allowing a new instance of Horizon to start while the last
    | instance will continue to terminate each of its workers.
    |
    */

    'fast_termination' => false,

    /*
    |--------------------------------------------------------------------------
    | Memory Limit (MB)
    |--------------------------------------------------------------------------
    |
    | This value describes the maximum amount of memory the Horizon worker
    | may consume before it is terminated and restarted. You should set
    | this value according to the resources available to your server.
    |
    */

    'memory_limit' => 64,

    /*
    |--------------------------------------------------------------------------
    | Queue Worker Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may define the queue worker settings used by your application
    | in all environments. These supervisors and settings handle all your
    | queued jobs and will be provisioned by Horizon during deployment.
    |
    */

    'environments' => [
        'production' => [

            'supervisor-tracking' => [
                'connection' => 'redis',
                'queue' => ['tracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 30,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-universal-tracking' => [
                'connection' => 'redis',
                'queue' => ['universalTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-lcs-tracking' => [
                'connection' => 'redis',
                'queue' => ['lcsTracking','lcsMerchantTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-tcs-tracking' => [
                'connection' => 'redis',
                'queue' => ['tcsTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-blueex-tracking' => [
                'connection' => 'redis',
                'queue' => ['blueExTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 30,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-callcourier-tracking' => [
                'connection' => 'redis',
                'queue' => ['callCourierTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],


            'supervisor-tpl-tracking' => [
                'connection' => 'redis',
                'queue' => ['tplRiderTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-mnp-tracking' => [
                'connection' => 'redis',
                'queue' => ['mnpTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-postex-tracking' => [
                'connection' => 'redis',
                'queue' => ['postExTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],


            'supervisor-daewoo-tracking' => [
                'connection' => 'redis',
                'queue' => ['daewooTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-trax-tracking' => [
                'connection' => 'redis',
                'queue' => ['traxTracking'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 3,
                'processes' => 3,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],

            'supervisor-all-payment' => [
                'connection' => 'redis',
                'queue' => ['traxPayment', 'blueExPayment', 'riderPayment','mnpPayment','callcourierPayment','movexPayment','tcsPayment','stallionPayment','lcsMerchantPayment'],
                'balance' => 'auto',
                'minProcesses' => 1,
                'maxProcesses' => 10,
                'processes' => 10,
                'tries' => 1,
                "timeout" => 60,
                'sleep' => 5, // Sleep time for idle workers
            ],
            'supervisor-payment' => [
                'connection' => 'redis',
                'queue' => ['lcsPayment'],
                'balance' => 'auto',
                'processes' => 5,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],
            'supervisor-payment' => [
                'connection' => 'redis',
                'queue' => ['postexPayment'],
                'balance' => 'auto',
                'processes' => 5,
                'tries' => 1,
                "timeout" => 300,
                'sleep' => 5, // Sleep time for idle workers
            ],
            'supervisor-request-limit' => [
                'connection' => 'redis',
                'queue' => ['shipment_status_change_trigger', 'khaadiTrackingDPD','paid_on_shopify','forrun_shipper_advice'],
                'balance' => 'simple',
                'minProcesses' => 1,
                'processes' => 4,
                'tries' => 1,
                'sleep' => 5, // Sleep time for idle workers
            ],
            'supervisor-amazon-only' => [
                'connection' => 'redis',
                'queue' => ['khaadiTrackingAmazonShipping'],
                'balance' => 'simple',
                'minProcesses' => 1,
                'processes' => 1,
                'tries' => 1,
                "timeout" => 120,
                'sleep' => 5, // Sleep time for idle workers
            ]
        ],



        'local' => [
            'supervisor-1' => [
                'connection' => 'redis',
                'queue' => ['default'],
                'balance' => 'simple',
                'processes' => 3,
                'tries' => 5,
            ],
        ],
    ],
];
